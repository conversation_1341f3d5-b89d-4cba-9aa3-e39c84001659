# GetAttendData函数缺陷报告

## 缺陷概述
- **函数名称**: GetAttendData
- **缺陷类型**: 数据源和逻辑不一致
- **严重程度**: 高
- **发现时间**: 2025-08-07
- **缺陷状态**: 待修复

## 问题描述
GetAttendData函数在PHP到Go迁移过程中存在数据源和业务逻辑不一致问题，导致到课数据计算和显示不符合预期。

## 代码位置对比
- **PHP版本**: `/Users/<USER>/Documents/interface/assistantdesk/models/service/page/deskv1/student/PerformanceV1.php`
- **Go版本**: `/Users/<USER>/Documents/interface/deskcrm/service/arkBase/lessonDataFactory/lessonDataFunc/lesson.go`

## 详细分析

### PHP版本实现
```php
private function getAttend(&$row, $currentLessonInfo) {
    $lessonLuData = $this->luData[$currentLessonInfo['lessonId']];
    $row['attend'] = ['-', 'gray', 1];
    
    // LBP章节特殊处理
    if ($currentLessonInfo['playType'] == self::PLAY_TYPE_LUBOKE) {
        $row['attend'] = ['-', 'gray', 1];
    } else {
        // 格式化到课时长
        $row['attend'][0] = $lessonLuData['attendDuration'] ? 
            $this->formatDuration($lessonLuData['attendDuration']) : '-';
        
        // 到课状态判断
        if (!$lessonLuData['attendDuration']) {
            // 检查请假状态
            if (isset($this->lessonStudentData[$currentLessonInfo['lessonId']][$this->studentId]['preAttend']) && 
                $this->lessonStudentData[$currentLessonInfo['lessonId']][$this->studentId]['preAttend'] == self::PRE_ATTEND_LEAVE) {
                $row['attend'] = ['请假', 'orange', 0];
            } else {
                $row['attend'] = ['未到', 'red', 0];
            }
        } elseif ($lessonLuData['attendDuration'] >= 30*60) {
            $row['attend'][1] = 'green';
        } else {
            $row['attend'][1] = 'orange';
        }
    }
}
```

### Go版本实现
```go
func (s *Format) GetAttendData(ctx *gin.Context) (err error) {
    // 获取学生课程数据 - attendDuration字段
    queryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLuData", []interface{}{
        []int64{s.param.CourseID},
        []int64{s.param.StudentUid},
        []string{"attendDuration"},
    })
    
    // 获取课程信息 - playType字段
    courseData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetCourseInfo", []interface{}{s.param.CourseID})
    
    // 获取学生章节请假信息 - 从DB获取
    lessonStudentData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLessonStudentData", []interface{}{
        s.param.CourseID, s.param.LessonIDs, []int64{s.param.StudentUid},
    })
    
    studentLessonData := queryData.(map[string]map[string]interface{})
    courseInfo := courseData.(dal.CourseInfo)
    studentLeaveData := lessonStudentData.(map[string]*models.LessonStudent)
    
    // 构建章节信息映射
    lessonMap := map[int64]dal.LessonInfo{}
    for lessonIDStr, lessonInfo := range courseInfo.LessonList {
        lessonID, _ := strconv.ParseInt(lessonIDStr, 10, 64)
        lessonMap[lessonID] = lessonInfo
    }
    
    const ATTEND_STATUS_LEAVE = 3
    
    for _, lessonID := range s.param.LessonIDs {
        attendData := ""
        attendCode := 0
        leaveSeason := ""
        
        key := fmt.Sprintf("%d_%d_%d", s.param.CourseID, lessonID, s.param.StudentUid)
        
        // 获取attendDuration
        attendDuration := int64(0)
        if lessonData, ok := studentLessonData[key]; ok {
            if duration, exists := lessonData["attendDuration"]; exists {
                if val, ok := duration.(int64); ok {
                    attendDuration = val
                }
            }
        }
        
        // 获取playType
        playType := int64(0)
        if lesson, ok := lessonMap[lessonID]; ok {
            playType = int64(lesson.PlayType)
        }
        
        // LBP章节特殊处理
        if playType == dal.PLAY_TYPE_LUBOKE {
            attendData = "-"
            attendDuration = 0
            attendCode = 1
        } else {
            // 格式化到课时长
            if attendDuration > 0 {
                attendData = FormatDuration(attendDuration)
            }
            
            // 到课状态判断逻辑
            if attendDuration == 0 {
                // 检查是否请假
                isLeave := false
                leaveReason := ""
                leaveSeasonText := ""
                
                studentKey := models.GetLessonStudentKey(lessonID, s.param.StudentUid)
                if studentInfo, ok := studentLeaveData[studentKey]; ok {
                    if studentInfo.PreAttend == models.PreAttendLeave {
                        isLeave = true
                        if studentInfo.ExtData.FirstLeaveReason != "" {
                            leaveReason = fmt.Sprintf("请假:#%s#", studentInfo.ExtData.FirstLeaveReason)
                        } else {
                            leaveReason = "请假"
                        }
                        if studentInfo.ExtData.LeaveSeason != "" {
                            leaveReason += studentInfo.ExtData.LeaveSeason
                        }
                        leaveSeasonText = studentInfo.ExtData.LeaveSeason
                    }
                }
                
                if isLeave {
                    attendCode = ATTEND_STATUS_LEAVE
                    attendData = leaveReason
                    leaveSeason = leaveSeasonText
                } else {
                    attendCode = 0
                    attendData = "未到"
                }
            } else if attendDuration >= 30*60 {
                attendCode = 1
            } else {
                attendCode = 2
            }
        }
        
        // 添加到课数据
        _ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, attendData)
        _ = s.AddOutputStudent(ctx, lessonID, "attendCode", attendCode)
        _ = s.AddOutputStudent(ctx, lessonID, "leaveSeason", leaveSeason)
    }
}
```

## 问题分析

### 1. 数据源不一致 ❌
- **PHP版本**: 
  - LU数据: `$this->luData` (ES数据)
  - 课程信息: `$currentLessonInfo` (直接传入)
  - 请假信息: `$this->lessonStudentData` (预加载数据)
- **Go版本**:
  - LU数据: `GetLuData` (通过dataQueryPoint访问ES)
  - 课程信息: `GetCourseInfo` (通过dataQueryPoint访问DAL)
  - 请假信息: `GetLessonStudentData` (通过dataQueryPoint访问DB)

### 2. 输出格式不一致 ❌
- **PHP版本**: `['显示文本', '颜色', '是否可点击']`
- **Go版本**: `显示文本` + 额外的`attendCode`和`leaveSeason`字段

### 3. 请假处理逻辑不一致 ❌
- **PHP版本**: 简单的请假状态显示
- **Go版本**: 复杂的请假原因和季节处理

### 4. 状态码逻辑不一致 ❌
- **PHP版本**: 使用颜色和点击状态表示
- **Go版本**: 使用数字状态码表示

## 验证结果

### 功能对比
| 功能点 | PHP版本 | Go版本 | 一致性 |
|--------|----------|----------|----------|
| LBP章节处理 | ✅ 显示"-" | ✅ 显示"-" | ✅ 一致 |
| 到课时长格式化 | ✅ XminYs格式 | ✅ XminYs格式 | ✅ 一致 |
| 请假状态检查 | ✅ 检查请假 | ✅ 检查请假 | ✅ 一致 |
| 请假显示格式 | ❌ 简单显示"请假" | ❌ 复杂格式"请假:#原因#季节" | ❌ 不一致 |
| 输出格式 | ❌ [文本,颜色,点击] | ❌ 文本+状态码 | ❌ 不一致 |

### 测试用例
**期望输出**: 与PHP版本相同的到课数据显示格式和逻辑
**实际输出**: 不同的显示格式和增强的请假处理逻辑

## 修复建议

### 方案1: 完全对齐PHP版本 (推荐)
1. 统一数据源获取方式
2. 简化请假显示逻辑
3. 统一输出格式为数组格式

### 方案2: 保持Go版本增强功能
如果需要保持Go版本的增强功能，需要：
1. 确保核心逻辑与PHP版本一致
2. 添加向后兼容的输出格式
3. 更新相关文档说明差异

## 修复优先级
- **优先级**: 高
- **影响范围**: 所有到课数据显示功能
- **业务影响**: 可能导致用户界面显示异常

## 验证标准

修复后的函数需要满足以下标准：
1. ✅ 数据源与PHP版本一致
2. ✅ 输出格式与PHP版本完全相同
3. ✅ 请假处理逻辑与PHP版本一致
4. ✅ 状态码逻辑与PHP版本一致
5. ✅ 通过所有边界条件测试

## 相关文件
- checklist.md (状态已更新为failed_qa)
- lesson.go (需要修复的源文件)
- PerformanceV1.php (PHP参考实现)

## 附加说明
此缺陷反映了迁移过程中的功能增强问题，虽然Go版本的功能更完整，但与PHP版本的不一致可能导致系统集成问题。建议在迁移过程中严格保持功能一致性，必要的增强应该在迁移完成后统一进行。