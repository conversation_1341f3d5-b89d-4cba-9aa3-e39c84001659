package dataproxy

type singleRet struct {
	List  []interface{} `mapstructure:"list"`
	Total int           `mapstructure:"total"`
}

type GetCourseLessonCommonDataNewParams struct {
	StudentUids []int64  `form:"studentUids"`
	CourseId    int64    `form:"courseId"`
	LessonId    int64    `form:"lessonId"`
	Fields      []string `form:"fields"`
}

type GetCourseStudentAggrParams struct {
	StudentUids []int64  `form:"studentUids"`
	CourseId    int64    `form:"courseId"`
	Fields      []string `form:"fields"`
}

type GetDimStudentListParams struct {
	StudentUids []int64
	Fields      []string
}

type GetCourseLessonCommonDataNewResp struct {
	StudentUid                           int64  `json:"student_uid" mapstructure:"student_uid"`
	CourseId                             int64  `json:"course_id" mapstructure:"course_id"`
	LessonId                             int64  `json:"lesson_id" mapstructure:"lesson_id"`
	BluetoothFirstConnectSuc             int64  `json:"bluetooth_first_connect_suc" mapstructure:"bluetooth_first_connect_suc"`
	BluetoothLastConnectStatus           int64  `json:"bluetooth_last_connect_status" mapstructure:"bluetooth_last_connect_status"`
	BluetoothLastConnectTimestamp        int64  `json:"bluetooth_last_connect_timestamp" mapstructure:"bluetooth_last_connect_timestamp"`
	IsBluetoothBeforeClassConnectSuc     int64  `json:"is_bluetooth_before_class_connect_suc" mapstructure:"is_bluetooth_before_class_connect_suc"`
	IsBluetoothConnectSuc                int64  `json:"is_bluetooth_connect_suc" mapstructure:"is_bluetooth_connect_suc"`
	IsBluetoothDuringClassConnectSuc     int64  `json:"is_bluetooth_during_class_connect_suc" mapstructure:"is_bluetooth_during_class_connect_suc"`
	IsSensorBeforeClassCheckSuc          int64  `json:"is_sensor_before_class_check_suc" mapstructure:"is_sensor_before_class_check_suc"`
	IsSensorCheckSuc                     int64  `json:"is_sensor_check_suc" mapstructure:"is_sensor_check_suc"`
	IsSensorDuringClassCheckSuc          int64  `json:"is_sensor_during_class_check_suc" mapstructure:"is_sensor_during_class_check_suc"`
	SensorFirstCheckSuc                  int64  `json:"sensor_first_check_suc" mapstructure:"sensor_first_check_suc"`
	SensorLastCheckStatus                int64  `json:"sensor_last_check_status" mapstructure:"sensor_last_check_status"`
	SensorLastCheckTimestamp             int64  `json:"sensor_last_check_timestamp" mapstructure:"sensor_last_check_timestamp"`
	Hdv2IsBluetoothBeforeClassConnectSuc *int64 `json:"hdv2_is_bluetooth_before_class_connect_suc" mapstructure:"hdv2_is_bluetooth_before_class_connect_suc"`
	Hdv2IsBluetoothDuringClassConnectSuc *int64 `json:"hdv2_is_bluetooth_during_class_connect_suc" mapstructure:"hdv2_is_bluetooth_during_class_connect_suc"`
	Hdv2IsBluetoothConnectSuc            *int64 `json:"hdv2_is_bluetooth_connect_suc" mapstructure:"hdv2_is_bluetooth_connect_suc"`
}

type GetCourseStudentAggrResp struct {
	StudentUid              int64 `json:"student_uid" mapstructure:"student_uid"`
	CourseId                int64 `json:"course_id" mapstructure:"course_id"`
	IsBluetoothConnectSucCu int64 `json:"is_bluetooth_connect_suc_cu" mapstructure:"is_bluetooth_connect_suc_cu"`
	IsSensorCheckSucCu      int64 `json:"is_sensor_check_suc_cu" mapstructure:"is_sensor_check_suc_cu"`
}

type GetDimStudentListResp struct {
	StudentUid             int64   `json:"student_uid" mapstructure:"student_uid"`
	HaveCartData           int64   `json:"have_cart_data" mapstructure:"have_cart_data"`
	DeerLiteracyDeviceType string  `json:"deer_literacy_device_type" mapstructure:"deer_literacy_device_type"`
	MidweekActiveRate      float64 `json:"midweek_active_rate" mapstructure:"midweek_active_rate"`
	WeekendActiveRate      float64 `json:"weekend_active_rate" mapstructure:"weekend_active_rate"`
	CourseConsumeLevel     string  `json:"course_consume_level" mapstructure:"course_consume_level"`
	NoCourseConsumeLevel   string  `json:"no_course_consume_level" mapstructure:"no_course_consume_level"`
}

type GetEsUONParams struct {
	StudentUids []int64  `form:"studentUids"`
	Fields      []string `form:"fields"`
}

type GetEsUONResp struct {
	StudentUid                        int64  `json:"student_uid" mapstructure:"student_uid"`
	DeerFingeringL2DirectoryFinishCnt int64  `json:"deer_fingering_l2_directory_finish_cnt" mapstructure:"deer_fingering_l2_directory_finish_cnt"`
	DeerFingeringStarFinishCnt        int64  `json:"deer_fingering_star_finish_cnt" mapstructure:"deer_fingering_star_finish_cnt"`
	AppDownloadStatus                 int64  `json:"app_download_status" mapstructure:"app_download_status"`
	PcLoginDeviceTYpe                 int64  `json:"pc_login_device_type" mapstructure:"pc_login_device_type"`
	PadLoginDeviceTYpe                int64  `json:"pad_login_device_type" mapstructure:"pad_login_device_type"`
	PhoneLoginDeviceType              int64  `json:"phone_login_device_type" mapstructure:"phone_login_device_type"`
	IsSubjectUser                     int64  `json:"is_subject_user" mapstructure:"is_subject_user"`
	IsSuYangUser                      int64  `json:"is_suyang_user" mapstructure:"is_suyang_user"`
	DeerProgrammingHardwareBindStatus *int64 `json:"deer_programming_hardware_bind_status" mapstructure:"deer_programming_hardware_bind_status"`
	XCHProgrammingHardwareBindStatus  *int64 `json:"xch_programming_hardware_bind_status" mapstructure:"xch_programming_hardware_bind_status"`
	XZProgrammingHardwareBindStatus   *int64 `json:"xz_programming_hardware_bind_status" mapstructure:"xz_programming_hardware_bind_status"`
}

type GetLiveEsInteractQuestionParam struct {
	StudentUids []int64  `form:"studentUids"`
	CourseId    int64    `form:"courseId"`
	LessonId    int64    `form:"lessonId"`
	Fields      []string `form:"fields"`
}

type GetLiveEsInteractQuestionResp struct {
	StudentUid           int64 `json:"student_uid" mapstructure:"student_uid"`
	InteractId           int64 `json:"interact_id" mapstructure:"interact_id"`
	CodeUploadSuccessCnt int64 `json:"code_upload_success_cnt" mapstructure:"code_upload_success_cnt"`
	CodeLoadSuccessCnt   int64 `json:"code_load_success_cnt" mapstructure:"code_load_success_cnt"`
}

type GetEsCuDataParam struct {
	CourseIds     []int64  `form:"courseId"`
	StudentUids   []int64  `form:"studentUids"`
	AssistantUids []int64  `form:"assistantUid"`
	Fields        []string `form:"fields"`
}

type GetEsCuDataResp struct {
	StudentUid           int64  `json:"student_uid" mapstructure:"student_uid"`
	CourseId             int64  `json:"course_id"  mapstructure:"course_id"`
	LatestExamTime       int64  `json:"latest_exam_time" mapstructure:"latest_exam_time"`
	LatestTimeInterval   string `json:"latest_time_interval" mapstructure:"latest_time_interval"`
	IsReservePhoneAccess int64  `json:"is_reserve_phone_access" mapstructure:"is_reserve_phone_access"`
	IsReservePhoneCover  int64  `json:"is_reserve_phone_cover" mapstructure:"is_reserve_phone_cover"`
	TradeId              int64  `json:"trade_id" mapstructure:"trade_id"`
}

type GetCommonLuParam struct {
	LessonId    int64    `form:"lessonId"`
	StudentUids []int64  `form:"studentUids"`
	Fields      []string `form:"fields"`
}

type GetCommonLuResp struct {
	StudentUid                                           int64  `json:"student_uid" mapstructure:"student_uid"`
	LessonId                                             int64  `json:"lesson_id" mapstructure:"lesson_id"`
	IsInClassTeacherRoomContentView5Minute               int64  `json:"is_inclass_teacher_room_content_view_5minute" mapstructure:"is_inclass_teacher_room_content_view_5minute"`
	IsDeerBluetoothBeforeClassConnectSuc                 int64  `json:"is_bluetooth_before_class_connect_suc" mapstructure:"is_bluetooth_before_class_connect_suc"`
	IsDeerBluetoothDuringClassConnectSuc                 int64  `json:"is_bluetooth_during_class_connect_suc" mapstructure:"is_bluetooth_during_class_connect_suc"`
	IsAiBeforeclassTeacherRoomContentViewFinish5Percent  int64  `json:"is_ai_beforeclass_teacher_room_content_view_finish_5percent" mapstructure:"is_ai_beforeclass_teacher_room_content_view_finish_5percent"`
	IsAiInclassTeacherRoomContentViewFinish5Percent      int64  `json:"is_ai_inclass_teacher_room_content_view_finish_5percent" mapstructure:"is_ai_inclass_teacher_room_content_view_finish_5percent"`
	IsAiAfterclassTeacherRoomContentViewFinish5Percent   int64  `json:"is_ai_afterclass_teacher_room_content_view_finish_5percent" mapstructure:"is_ai_afterclass_teacher_room_content_view_finish_5percent"`
	IsAiBeforeclassTeacherRoomContentViewFinishThreeFour int64  `json:"is_ai_beforeclass_teacher_room_content_view_finish_three_four" mapstructure:"is_ai_beforeclass_teacher_room_content_view_finish_three_four"`
	IsInclassTeacherRoomContentViewFinishThreeFour       int64  `json:"is_inclass_teacher_room_content_view_finish_three_four" mapstructure:"is_inclass_teacher_room_content_view_finish_three_four"`
	IsAiAfterclassTeacherRoomContentViewFinishThreeFour  int64  `json:"is_ai_afterclass_teacher_room_content_view_finish_three_four" mapstructure:"is_ai_afterclass_teacher_room_content_view_finish_three_four"`
	InclassAnswerStatus                                  int64  `json:"inclass_answer_status" mapstructure:"inclass_answer_status"`
	BeforeAnswerStatus                                   int64  `json:"before_answer_status" mapstructure:"before_answer_status"`
	AfterAnswerStatus                                    int64  `json:"after_answer_status" mapstructure:"after_answer_status"`
	InclassParticipateCnt                                int64  `json:"inclass_participate_cnt" mapstructure:"inclass_participate_cnt"` // 课堂参与次数
	InclassQuestionCnt                                   int64  `json:"inclass_question_cnt" mapstructure:"inclass_question_cnt"`       // 课堂作答次数
	InclassRightCnt                                      int64  `json:"inclass_right_cnt" mapstructure:"inclass_right_cnt"`             // 课堂答对次数
	Exam58                                               string `json:"exam58" mapstructure:"exam58"`
	Exam61                                               string `json:"exam61" mapstructure:"exam61"`
	Exam60                                               string `json:"exam60" mapstructure:"exam60"`
	Exam62                                               string `json:"exam62" mapstructure:"exam62"`
	InclassLpOpenMouthCnt                                int64  `json:"inclass_lp_open_mouth_cnt" mapstructure:"inclass_lp_open_mouth_cnt"`
	PreciseFirstCorrectCnt                               int64  `json:"precise_first_correct_cnt" mapstructure:"precise_first_correct_cnt"`
	PreciseFirstRightCnt                                 int64  `json:"precise_first_right_cnt" mapstructure:"precise_first_right_cnt"`
	PreciseFirstSubmitTime                               int64  `json:"precise_first_submit_time" mapstructure:"precise_first_submit_time"`
	IsInclassTeacherRoomViewFinishV1                     int64  `json:"is_inclass_teacher_room_view_finish_v1" mapstructure:"is_inclass_teacher_room_view_finish_v1"`                                 //是否观看完课75%
	IsInclassTeacherRoomTotalPlaybackThreeFourV1         int64  `json:"is_inclass_teacher_room_total_playback_three_four_v1" mapstructure:"is_inclass_teacher_room_total_playback_three_four_v1"`     //是否lbp观看完课75%
	IsInclassTeacherRoomZpluspContentView5min            int64  `json:"is_inclass_teacher_room_zplusp_content_view_5min" mapstructure:"is_inclass_teacher_room_zplusp_content_view_5min"`             //直播在线时长+回放内容观看时长>=5min
	IsInclassTeacherRoomZpluspContentViewThreeFour       int64  `json:"is_inclass_teacher_room_zplusp_content_view_three_four" mapstructure:"is_inclass_teacher_room_zplusp_content_view_three_four"` //直播在线时长+回放内容观看时长>=章节总时长3/4
	InclassTeacherRoomZpluspContentViewDuration          int64  `json:"inclass_teacher_room_zplusp_content_view_duration" mapstructure:"inclass_teacher_room_zplusp_content_view_duration"`           //直播在线时长+回放内容观看时长
	Exam7                                                string `json:"exam7" mapstructure:"exam7"`                                                                                                   //巩固练习
}

var CommonLuExam7Stru struct {
	CorrectLevel   int `json:"correct_level" mapstructure:"correct_level"`
	LastSubmitTime int `json:"last_submit_time" mapstructure:"last_submit_time"`
}

type GetCommonCuParam struct {
	CourseId    int64    `form:"courseId"`
	StudentUids []int64  `form:"studentUids"`
	Fields      []string `form:"fields"`
}

type GetCommonCuResp struct {
	StudentUid                           int64 `json:"student_uid" mapstructure:"student_uid"`
	CourseId                             int64 `json:"course_id" mapstructure:"course_id"`
	LastPlaybackTimePoint                int64 `json:"last_playback_timepoint" mapstructure:"last_playback_timepoint"`
	LastPlaybackDuration                 int64 `json:"last_playback_duration" mapstructure:"last_playback_duration"`
	LastPlaybackLessonId                 int64 `json:"last_playback_lesson_id" mapstructure:"last_playback_lesson_id"`
	AttendMainLesson5MinutesNum          int64 `json:"attend_5minute_main_lesson_num" mapstructure:"attend_5minute_main_lesson_num"`
	AttendOrContentMainLesson5MinutesNum int64 `json:"attend_or_content_view_5minute_main_lesson_num" mapstructure:"attend_or_content_view_5minute_main_lesson_num"`
	AttendOrContentMainLessonNum         int64 `json:"attend_or_content_view_three_four_main_lesson_num" mapstructure:"attend_or_content_view_three_four_main_lesson_num"`
	Exam58SubmitCnt                      int64 `json:"exam58_submit_cnt" mapstructure:"exam58_submit_cnt"`
	Exam58ShouldSubmitCnt                int64 `json:"exam58_should_submit_cnt" mapstructure:"exam58_should_submit_cnt"`
	Exam61SubmitCnt                      int64 `json:"exam61_submit_cnt" mapstructure:"exam61_submit_cnt"`
	Exam61ShouldSubmitCnt                int64 `json:"exam61_should_submit_cnt" mapstructure:"exam61_should_submit_cnt"`
	OpenMouthCnt                         int64 `json:"open_mouth_cnt" mapstructure:"open_mouth_cnt"`
	ZpluspContentView5minJdLessonNumCu   int64 `json:"zplusp_content_view_5min_jd_lesson_num_cu" mapstructure:"zplusp_content_view_5min_jd_lesson_num_cu"`
	ZpluspContentView5minBdLessonNumCu   int64 `json:"zplusp_content_view_5min_bd_lesson_num_cu" mapstructure:"zplusp_content_view_5min_bd_lesson_num_cu"`
	ZpluspContentView5minDxjqLessonNumCu int64 `json:"zplusp_content_view_5min_dxjq_lesson_num_cu" mapstructure:"zplusp_content_view_5min_dxjq_lesson_num_cu"`
	Exam60TotalSubmitLessonCnt           int64 `json:"exam60_total_submit_lesson_cnt" mapstructure:"exam60_total_submit_lesson_cnt"`
}

type GetAssistantCourseLuParam struct {
	LessonId    int64    `form:"lessonId"`
	StudentUids []int64  `form:"studentUids"`
	Fields      []string `form:"fields"`
}

type GetAssistantCourseLuResp struct {
	StudentUid                             int64   `json:"student_uid" mapstructure:"student_uid"`
	LessonId                               int64   `json:"lesson_id" mapstructure:"lesson_id"`
	IsAssistantcourseNeedAttend            int64   `json:"is_assistantcourse_need_attend" mapstructure:"is_assistantcourse_need_attend"`
	AssistantcourseAppointmentCourseNum    int64   `json:"assistantcourse_appointment_course_num" mapstructure:"assistantcourse_appointment_course_num"`
	AssistantcourseAppointmentCourseDetail string  `json:"assistantcourse_appointment_course_detail" mapstructure:"assistantcourse_appointment_course_detail"`
	AssistantcourseAppointmentCourseIdList []int64 `json:"assistantcourse_appointment_course_id_list" mapstructure:"assistantcourse_appointment_course_id_list"`
}

type GetExternalIdlLpcLeadsL2NewParam struct {
	LeadsIds []int64  `form:"leads_ids"`
	Fields   []string `form:"fields"`
}

type GetExternalIdlLpcLeadsL2NewResp struct {
	LeadsId               int64   `json:"leads_id" mapstructure:"leads_id"`
	CourseId              int64   `json:"course_id" mapstructure:"course_id"`
	StudentUid            int64   `json:"student_uid" mapstructure:"student_uid"`
	ScoreX                float64 `json:"scorex" mapstructure:"scorex"`                                 //大模型分层打分
	RegisterDuration      float64 `json:"register_duration" mapstructure:"register_duration"`           //注册时间距离灌班时间间隔
	CityName              string  `json:"city_name" mapstructure:"city_name"`                           // 城市名称
	CityLevel             int64   `json:"city_level" mapstructure:"city_level"`                         // 城市级别
	NoMainAcrossOneSeason int64   `json:"nomain_acrossoneseason" mapstructure:"nomain_acrossoneseason"` //历史购买短训班学季数
	TradeAmt              float64 `json:"trade_amt" mapstructure:"trade_amt"`                           // 成交金额
	YearPhoneAccessNum    int64   `json:"year_phone_accessnum" mapstructure:"year_phone_accessnum"`     // 近一年内电话接通成功次数
}

type GetListByCourseIdLessonIdsAssistantUidParam struct {
	CourseId     int64    `json:"courseId"`
	LessonIds    []int64  `json:"lessonIds"`
	AssistantUid int64    `json:"assistantUid"`
	Fields       []string `json:"fields"`
}

type ExamAnswer struct {
	TotalNum       int64 `json:"total_num"`
	IsHave         int64 `json:"is_have"`
	SubmitTime     int64 `json:"submit_time"`
	RightNum       int64 `json:"right_num"`
	ParticipateNum int64 `json:"participate_num"`
	Status         int64 `json:"status"`
}

type GetStudentLessonDataCommonResp struct {
	StudentUid                  int64                 `json:"studentUid"`
	ExerciseRightNum            int64                 `json:"exerciseRightNum"`
	ExerciseParticipateNum      int64                 `json:"exerciseParticipateNum"`
	TradeStatus                 int64                 `json:"tradeStatus"`
	LessonId                    int64                 `json:"lessonId"`
	ExerciseTotalNum            int64                 `json:"exerciseTotalNum"`
	PreviewTotalNum5            int64                 `json:"previewTotalNum5"`
	HomeworkPracticeTotalNum    int64                 `json:"homeworkPracticeTotalNum"`
	IlabHomeworkGeXingTotalNum  int64                 `json:"ilabHomeworkGeXingTotalNum"`
	StageTestExamTotalNum       int64                 `json:"stageTestExamTotalNum"`
	TangTangExamTotalNum        int64                 `json:"tangTangExamTotalNum"`
	SynchronousPracticeTotalNum int64                 `json:"synchronousPracticeTotalNum"`
	PreviewTotalNum13           int64                 `json:"previewTotalNum13"`
	HaveOralQuestion            int64                 `json:"haveOralQuestion"`
	OralQuestionTotalNum        int64                 `json:"oralQuestionTotalNum"`
	WordpracticeTotalNum        int64                 `json:"wordpracticeTotalNum"`
	WordlearnTotalNum           int64                 `json:"wordlearnTotalNum"`
	ImproveTotalNum             int64                 `json:"improveTotalNum"`
	GrammarpracticeTotalNum     int64                 `json:"grammarpracticeTotalNum"`
	PracticesTotalNum           int64                 `json:"practicesTotalNum"`
	InTestTotalNum              int64                 `json:"inTestTotalNum"`
	ExamAnswer                  map[string]ExamAnswer `json:"exam_answer"`
	AttendLabel                 interface{}           `json:"attendLabel"`
	InteractionLabel            interface{}           `json:"interactionLabel"`
	MainDepartment              int64                 `json:"mainDepartment"`
	IsPreviewFinish             int64                 `json:"isPreviewFinish"`
	PreviewFinishTime           int64                 `json:"previewFinishTime"`
	PreviewParticipateNum       int64                 `json:"previewParticipateNum"`
	PreviewCorrectNum           int64                 `json:"previewCorrectNum"`
	PreviewTotalNum             int64                 `json:"previewTotalNum"`
	PreviewIsExpound            int64                 `json:"previewIsExpound"`
	PlaybackAttendLabel         []int64               `json:"playbackAttendLabel"`
	LessonStopTime              int64                 `json:"lessonStopTime"`
	LessonStartTime             int64                 `json:"lessonStartTime"`
	PlaybackTotalTime           int64                 `json:"playbackTotalTime"`
	PlaybackInteractionLabel    []int64               `json:"playbackInteractionLabel"`
}

type GetListByBindIdsBindTypeRelationTypesCommonParam struct {
	BindType      int64    `json:"bindType"`
	BindIds       []int64  `json:"bindIds"`
	RelationTypes []int    `json:"relationTypes"`
	ExamTags      []int64  `json:"examTags"`
	Fields        []string `json:"fields"`
}

type GetListByBindIdsBindTypeRelationTypesCommonResp struct {
	BindId       int64 `json:"bindId"`
	RelationType int64 `json:"relationType"`
	TotalNum     int64 `json:"totalNum"`
	BindStatus   int64 `json:"bindStatus"`
}

type GetLpcListByLessonLeadsParam struct {
	CourseId int64    `json:"courseId"`
	LessonId int64    `json:"lessonId"`
	LeadsIds []int64  `json:"leadsIds"`
	Fields   []string `json:"fields"`
}

type GetLpcListByLessonLeadsResp struct {
	LeadsId                                int64              `json:"leads_id" mapstructure:"leads_id"`
	LessonId                               int64              `json:"lesson_id"  mapstructure:"lesson_id"`
	StudentUid                             int64              `json:"student_uid" mapstructure:"student_uid"`
	Duration                               int64              `json:"duration"`
	IsPlayback                             int64              `json:"is_playback"`
	MainDepartment                         int64              `json:"main_department"`
	IsAttendFinish                         int64              `json:"is_attend_finish"`
	IsAiFinish                             int64              `json:"is_ai_finish"`
	IsLbpAttendFinish                      int64              `json:"is_lbp_attend_finish"`
	Attend                                 int64              `json:"attend"`
	IsAiAttend                             int64              `json:"is_ai_attend"`
	IsLbpAttend                            int64              `json:"is_lbp_attend"`
	OpenMouthCnt                           int64              `json:"open_mouth_cnt" mapstructure:"open_mouth_cnt"`
	IsAssistantcourseAttend                int64              `json:"is_assistantcourse_attend" mapstructure:"is_assistantcourse_attend"`
	IsAssistantcourseNeedAttend            int64              `json:"is_assistantcourse_need_attend" mapstructure:"is_assistantcourse_need_attend"`
	IsAssistantcourseFinish                int64              `json:"is_assistantcourse_finish" mapstructure:"is_assistantcourse_finish"`
	AssistantcourseAppointmentType         int64              `json:"assistantcourse_appointment_type" mapstructure:"assistantcourse_appointment_type"`
	AssistantcourseAppointmentCourseNum    int64              `json:"assistantcourse_appointment_course_num" mapstructure:"assistantcourse_appointment_course_num"`
	AssistantcourseAppointmentCourseDetail string             `json:"assistantcourse_appointment_course_detail" mapstructure:"assistantcourse_appointment_course_detail"`
	Info                                   interface{}        `json:"info"`
	Labels                                 []*LabelItem       `json:"labels"`
	Exam1                                  *ExamAnswer        `json:"exam1"`
	Exam10                                 *ExamAnswer        `json:"exam10"`
	Exam13                                 *ExamAnswer        `json:"exam13"`
	Exam5                                  *ExamAnswer        `json:"exam5"`
	Exam58                                 *ExamPronunciation `json:"exam58"`
}

type ExamPronunciation struct {
	ExamId             int64   `json:"exam_id" mapstructure:"exam_id"`
	IsSubmit           int64   `json:"is_submit" mapstructure:"is_submit"`
	PronunciationScore float64 `json:"pronunciation_score" mapstructure:"pronunciation_score"`
	PronunciationStar  int64   `json:"pronunciation_star" mapstructure:"pronunciation_star"`
	IsCorrect          int64   `json:"is_correct" mapstructure:"is_correct"`
	CorrectTime        int64   `json:"correct_time" mapstructure:"correct_time"`
	SubmitTime         int64   `json:"submit_time" mapstructure:"submit_time"`
}

type ExamCommon struct {
	ExamId    int64 `json:"exam_id" mapstructure:"exam_id"`
	IsSubmit  int64 `json:"is_submit" mapstructure:"is_submit"`
	RightNum  int64 `json:"right_num" mapstructure:"right_num"`
	SubmitNum int64 `json:"submit_num" mapstructure:"submit_num"`
}

type Show struct {
	Type interface{} `json:"type"`
}

type Answer struct {
	Type interface{} `json:"type"`
}

type LabelItem struct {
	Label  interface{} `json:"label"`
	Show   Show        `json:"show"`
	Answer Answer      `json:"answer"`
}

type GetIdlLpcLeadsDataByLeadsIdsParam struct {
	LeadsIds []int64  `form:"leads_ids"`
	Fields   []string `form:"fields"`
}

type GetIdlLpcLeadsDataByLeadsIdsRespOri struct {
	LeadsId                   string      `json:"leads_id"`
	LpcUid                    string      `json:"lpc_uid"`
	CourseId                  string      `json:"course_id"`
	PreviewNum                string      `json:"preview_num"`
	LbpFinishNum              string      `json:"lbp_finish_num"`
	FinishNum                 string      `json:"finish_num"`
	PlaybackNum               string      `json:"playback_num"`
	LbpAttendNum              string      `json:"lbp_attend_num"`
	AttendNum                 string      `json:"attend_num"`
	PlacementTestSubmitDetail interface{} `json:"placement_test_submit_detail"`
}

type GetIdlLpcLeadsDataByLeadsIdsResp struct {
	LeadsId                   int64       `json:"leads_id" mapstructure:"leads_id"`
	LpcUid                    int64       `json:"lpc_uid" mapstructure:"lpc_uid"`
	CourseId                  int64       `json:"course_id" mapstructure:"course_id"`
	PreviewNum                int64       `json:"preview_num" mapstructure:"preview_num"`
	LbpFinishNum              int64       `json:"lbp_finish_num" mapstructure:"lbp_finish_num"`
	FinishNum                 int64       `json:"finish_num" mapstructure:"finish_num"`
	PlaybackNum               int64       `json:"playback_num" mapstructure:"playback_num"`
	LbpAttendNum              int64       `json:"lbp_attend_num" mapstructure:"lbp_attend_num"`
	AttendNum                 int64       `json:"attend_num" mapstructure:"attend_num"`
	PlacementTestSubmitDetail interface{} `json:"placement_test_submit_detail" mapstructure:"placement_test_submit_detail"`
}

type GetExternalIdlLpcLeadsDataByLeadsidResp struct {
	LeadsId       int64  `json:"leads_id" mapstructure:"leads_id"`
	LevelId       string `json:"level_id" mapstructure:"level_id"`
	FallLevelId   int64  `json:"fall_level_id" mapstructure:"fall_level_id"`
	BeforeLevelId int64  `json:"before_level_id" mapstructure:"finibefore_level_idsh_num"`
	AfterLevelId  int64  `json:"after_level_id" mapstructure:"after_level_id"`
}

type GetLpcListByCourseLeadsResp struct {
	LeadsId          int64 `json:"leads_id" mapstructure:"leads_id"`
	LastPlaybackTime int64 `json:"last_playback_time" mapstructure:"last_playback_time"`
}

type GetEsCuOrderByCAParam struct {
	CourseId     int64    `form:"courseId"`
	AssistantUid int64    `form:"assistantUid"`
	Fields       []string `form:"fields"`
}

type GetEsCuOrderByCAResp struct {
	CourseId                 int64  `json:"course_id" mapstructure:"course_id"`
	AssistantUid             int64  `json:"assistant_uid" mapstructure:"assistant_uid"`
	StudentUid               int64  `json:"student_uid" mapstructure:"student_uid"`
	Status                   int64  `json:"status" mapstructure:"status"` // 订单状态： 0:待支付 1:已支付 2:退款中 3:已退款 5:订单关闭
	SkuId                    int64  `json:"sku_id" mapstructure:"sku_id"`
	DoubtReserveSubjects     string `json:"doubt_reserve_subjects" mapstructure:"doubt_reserve_subjects"`
	DoubtNotReserveSubjects  string `json:"doubt_not_reserve_subjects" mapstructure:"doubt_not_reserve_subjects"`
	IsRefundSameSubjectMKB   int64  `json:"is_refund_same_subject_mkb" mapstructure:"is_refund_same_subject_mkb"`
	RefundSameSubjectMKBTime int64  `json:"refund_same_subject_mkb_time" mapstructure:"refund_same_subject_mkb_time"`
	IsSingleExpandReserve    int64  `json:"is_single_expand_reserve" mapstructure:"is_single_expand_reserve"`
	IsMultiExpandReserve     int64  `json:"is_multi_expand_reserve" mapstructure:"is_multi_expand_reserve"`
	IsExpandReserve          int64  `json:"is_expand_reserve" mapstructure:"is_expand_reserve"`
	ExpandReserveSubjects    string `json:"expand_reserve_subjects" mapstructure:"expand_reserve_subjects"`
	LongSeasonReverseTime    int64  `json:"long_season_reserve_time" mapstructure:"long_season_reserve_time"`
}

type GetEsCuOrderByCUParam struct {
	CourseId    int64    `form:"course_id"`
	StudentUids []int64  `form:"studentUids"`
	Fields      []string `form:"fields"`
}

type GetEsCuOrderByCUResp struct {
	CourseId   int64 `json:"course_id" mapstructure:"course_id"`
	StudentUid int64 `json:"student_uid" mapstructure:"student_uid"`
	TradeId    int64 `json:"trade_id" mapstructure:"trade_id"`
}

type GetEsStudentAppDataParam struct {
	StudentUids []int64  `json:"studentUids"`
	Fields      []string `json:"fields"`
	AppType     int64    `json:"appType"`
}

type GetEsStudentAppDataResp struct {
	AppType              int64  `json:"app_type" mapstructure:"app_type"`
	StudentUid           int64  `json:"student_uid" mapstructure:"student_uid"`
	AppDownloadStatus    string `json:"app_download_status" mapstructure:"app_download_status"`
	PcLoginDeviceType    string `json:"pc_login_device_type" mapstructure:"pc_login_device_type"`
	PadLoginDeviceType   string `json:"pad_login_device_type" mapstructure:"pad_login_device_type"`
	PhoneLoginDeviceType string `json:"phone_login_device_type" mapstructure:"phone_login_device_type"`
	LoginDeviceTime      int64  `json:"login_device_time" mapstructure:"login_device_time"`
}

type GetEsIdlUDataParam struct {
	StudentUids []int64  `json:"studentUids"`
	Fields      []string `json:"fields"`
}

type GetEsIdlUDataResp struct {
	StudentUid         int64  `json:"student_uid" mapstructure:"student_uid"`
	YCLStatus          int64  `json:"ycl_status,omitempty" mapstructure:"ycl_status"`
	YCLReviewTime      int64  `json:"ycl_review_time,omitempty" mapstructure:"ycl_review_time"`
	YCLReviewMsg       string `json:"ycl_review_msg,omitempty" mapstructure:"ycl_review_msg"`
	YCLCreateTime      int64  `json:"ycl_create_time,omitempty" mapstructure:"ycl_create_time"`
	YCLUpdateTime      int64  `json:"ycl_update_time,omitempty" mapstructure:"ycl_update_time"`
	LpcTradeNum        int64  `json:"lpc_trade_num,omitempty" mapstructure:"lpc_trade_num"`
	LpcTradeNum6mon    int64  `json:"lpc_trade_num_6mon,omitempty" mapstructure:"lpc_trade_num_6mon"`
	LpcTradeNum1y      int64  `json:"lpc_trade_num_1y,omitempty" mapstructure:"lpc_trade_num_1y"`
	LastLpcTradeTime   int64  `json:"last_lpc_trade_time,omitempty" mapstructure:"last_lpc_trade_time"`
	BankeTradeTime     int64  `json:"banke_trade_time,omitempty" mapstructure:"banke_trade_time"`
	LastBankeTradeTime int64  `json:"last_banke_trade_time,omitempty" mapstructure:"last_banke_trade_time"`
	UserLabel          int64  `json:"user_label,omitempty" mapstructure:"user_label"`
}

type GetStudentOrderDetailReq struct {
	SkuId          int64    `json:"skuId" form:"skuId"`
	StudentUidList []int64  `json:"studentUidList" form:"studentUidList"`
	ShopId         int64    `json:"shopId" form:"shopId"`
	Fields         []string `json:"fields" form:"fields"`
}

type GetStudentOrderDetailResp struct {
	Address                 GetStudentOrderDetailAddressInfoResp `json:"address" mapstructure:"address"`
	ExtCambridgeEnglishInfo GetStudentOrderCambridgeEnglishResp  `json:"ext_cambridge_english_info" mapstructure:"ext_cambridge_english_info"`
	PayTime                 int                                  `json:"pay_time" mapstructure:"pay_time"`
	ShopId                  string                               `json:"shop_id"  mapstructure:"shop_id"`
	SkuIdList               []string                             `json:"sku_id_list" mapstructure:"sku_id_list"`
	SkuRowList              []GetStudentOrderDetailSkuResp       `json:"sku_row_list" mapstructure:"sku_row_list"`
	OrderStatus             int64                                `json:"order_status" mapstructure:"order_status"`
	StudentUid              int64                                `json:"student_uid" mapstructure:"student_uid"`
}

type GetStudentOrderDetailSkuResp struct {
	PaidAmount int    `json:"paid_amount"  mapstructure:"paid_amount"`
	SkuId      string `json:"sku_id"  mapstructure:"sku_id"`
	SkuName    string `json:"sku_name"  mapstructure:"sku_name"`
}
type GetStudentOrderCambridgeEnglishResp struct {
	Level         string `json:"level"  mapstructure:"level"`
	Grade         string `json:"grade"  mapstructure:"grade"`
	SourceChannel string `json:"source_channel"  mapstructure:"source_channel"`
}

type GetStudentOrderDetailAddressInfoResp struct {
	Address      string `json:"address" mapstructure:"address"`
	AddressId    int    `json:"addressId" mapstructure:"address_id"`
	City         string `json:"city" mapstructure:"city"`
	CityId       int    `json:"cityId" mapstructure:"city_id"`
	Name         string `json:"name" mapstructure:"name"`
	Phone        string `json:"phone" mapstructure:"phone"`
	Prefecture   string `json:"prefecture" mapstructure:"prefecture"`
	PrefectureId int    `json:"prefectureId" mapstructure:"prefecture_id"`
	Province     string `json:"province" mapstructure:"province"`
	ProvinceId   int    `json:"provinceId" mapstructure:"province_id"`
	Town         string `json:"town" mapstructure:"town"`
	TownId       int    `json:"townId" mapstructure:"town_id"`
}

type GetEsPublicSeaClueInfoReq struct {
	ClueIds []string
	Fields  []string
}

type GetEsPublicSeaClueInfoResp struct {
	ClueId        string `json:"clue_id" mapstructure:"clue_id"`
	StudentUID    int64  `json:"student_uid" mapstructure:"student_uid"`
	CourseId      int64  `json:"course_id" mapstructure:"course_id"`
	Batch         string `json:"batch" mapstructure:"batch"`
	MainGradeId   int64  `json:"main_grade_id" mapstructure:"main_grade_id"`
	MainSubjectId int64  `json:"main_subject_id" mapstructure:"main_subject_id"`
	Season        string `json:"year_season" mapstructure:"year_season"`
	Semester      string `json:"season_semester" mapstructure:"season_semester"`
	LastFrom      string `json:"last_from" mapstructure:"last_from"`
	ProvinceName  string `json:"province_name" mapstructure:"province_name"`
	CityName      string `json:"city_name"  mapstructure:"city_name"`
	CityLevel     int64  `json:"city_level"  mapstructure:"city_level"`
	UserType      int64  `json:"user_type"  mapstructure:"user_type"`
	ExpiredTime   int64  `json:"expire_time"  mapstructure:"expire_time"`
	AttendTimes   int64  `json:"attend_times"  mapstructure:"attend_times"`
	FinishTimes   int64  `json:"finish_times"  mapstructure:"finish_times"`
}

type GetListByUnitIdStudentUidsReq struct {
	UnitID      int64    `json:"unitId" form:"unitId"`
	StudentUids []int64  `json:"studentUids" form:"studentUids"`
	Fields      []string `json:"fields" form:"fields"`
}

type GetListByUnitIdStudentUidsResp struct {
	UnitID                               int64 `json:"unit_id"  mapstructure:"unit_id"`
	StudentUID                           int64 `json:"student_uid"  mapstructure:"student_uid"`
	ZpluspContentView5minJdLessonNumEu   int64 `json:"zplusp_content_view_5min_jd_lesson_num_eu"  mapstructure:"zplusp_content_view_5min_jd_lesson_num_eu"`
	ZpluspContentView5minBdLessonNumEu   int64 `json:"zplusp_content_view_5min_bd_lesson_num_eu"  mapstructure:"zplusp_content_view_5min_bd_lesson_num_eu"`
	ZpluspContentView5minDxjqLessonNumEu int64 `json:"zplusp_content_view_5min_dxjq_lesson_num_eu"  mapstructure:"zplusp_content_view_5min_dxjq_lesson_num_eu"`
}

type GetStudentExprInfoDataReq struct {
	CourseId    int64    `form:"courseId"`
	StudentUids []int64  `form:"studentUids"`
	Fields      []string `form:"fields"`
}

type GetStudentExprInfoDataResp struct {
	StudentUID     int64  `json:"student_uid" mapstructure:"student_uid"`
	CourseId       int64  `json:"course_id" mapstructure:"course_id"`
	ExprStatus     string `json:"expr_status" mapstructure:"expr_status"`
	SignedExprNum  int64  `json:"signed_expr_num" mapstructure:"signed_expr_num"`
	TransitExprNum int64  `json:"transit_expr_num" mapstructure:"transit_expr_num"`
	TotalExprNum   int64  `json:"total_expr_num" mapstructure:"total_expr_num"`
	InnerExprNum   int64  `json:"inner_expr_num" mapstructure:"inner_expr_num"`
	ResendExprNum  int64  `json:"resend_expr_num" mapstructure:"resend_expr_num"`
}

type GetStudentOrderDataByCUReq struct {
	CourseId    int64    `form:"courseId"`
	StudentUids []int64  `form:"studentUids"`
	Fields      []string `form:"fields"`
}

type GetStudentOrderDataByCUResp struct {
	StudentUID int64 `json:"student_uid" mapstructure:"student_uid"`
	CourseId   int64 `json:"product_id" mapstructure:"product_id"`
	OrderId    int64 `json:"order_id" mapstructure:"order_id"`
}

type GetStudentLatestDelaminationDataByCAReq struct {
	CourseId     int64    `form:"courseId"`
	StudentUid   int64    `form:"studentUid"`
	AssistantUid int64    `form:"assistantUid"`
	Fields       []string `form:"fields"`
}

type GetListByCourseIdAssistantUidSaveTimeReq struct {
	CourseId     int64    `form:"courseId"`
	StudentUid   int64    `form:"studentUid"`
	AssistantUid int64    `form:"assistantUid"`
	SaveTime     string   `form:"saveTime"`
	Fields       []string `form:"fields"`
}

type GetStudentLatestDelaminationDataByCAResp struct {
	CourseId                                int64  `json:"course_id" mapstructure:"course_id"`
	AssistantUid                            int64  `json:"assistantUid" mapstructure:"assistantUid"`
	SaveTime                                string `json:"saveTime" mapstructure:"saveTime"`
	StudentUID                              int64  `json:"studentUid" mapstructure:"studentUid"`
	PreclassAttendNum                       int64  `json:"preclassAttendNum" mapstructure:"preclassAttendNum"`
	PreclassFinishAttendLessonNum           int64  `json:"preclassFinishAttendLessonNum" mapstructure:"preclassFinishAttendLessonNum"`
	PlaybackParticipateNumNew               int64  `json:"playback_participate_num_new" mapstructure:"playback_participate_num_new"`
	PlaybackRightNumNew                     int64  `json:"playback_right_num_new" mapstructure:"playback_right_num_new"`
	AttendLongLessonNumWithLbp              int64  `json:"attend_long_lesson_num_with_lbp" mapstructure:"attend_long_lesson_num_with_lbp"`
	AttendFinishLessonNumWithLbp            int64  `json:"attend_finish_lesson_num_with_lbp" mapstructure:"attend_finish_lesson_num_with_lbp"`
	PlaybackParticipateNumWithLbp           int64  `json:"playback_participate_num_with_lbp" mapstructure:"playback_participate_num_with_lbp"`
	PlaybackRightNumWithLbp                 int64  `json:"playback_right_num_with_lbp" mapstructure:"playback_right_num_with_lbp"`
	AttendLongLessonNum                     int64  `json:"attendLongLessonNum" mapstructure:"attendLongLessonNum"`
	AttendFinishLessonNum                   int64  `json:"attendFinishLessonNum" mapstructure:"attendFinishLessonNum"`
	AssistantcourseAttendFinishNum          int64  `json:"assistantcourse_attend_finish_num" mapstructure:"assistantcourse_attend_finish_num"`
	AssistantcourseAttendNum                int64  `json:"assistantcourse_attend_num" mapstructure:"assistantcourse_attend_num"`
	AttendLongLessonNumTimeless             int64  `json:"attendLongLessonNumTimeless" mapstructure:"attendLongLessonNumTimeless"`
	AttendFinishLessonNumTimeless           int64  `json:"attendFinishLessonNumTimeless" mapstructure:"attendFinishLessonNumTimeless"`
	PreviewExamFinishNum                    int64  `json:"previewExamFinishNum" mapstructure:"previewExamFinishNum"`
	AlwaysTestExamFinishNum                 int64  `json:"alwaysTestExamFinishNum" mapstructure:"alwaysTestExamFinishNum"`
	AlwaysTestExamRightNum                  int64  `json:"alwaysTestExamRightNum" mapstructure:"alwaysTestExamRightNum"`
	PlaybackParticipateNum                  int64  `json:"playbackParticipateNum" mapstructure:"playbackParticipateNum"`
	PlaybackRightNum                        int64  `json:"playbackRightNum" mapstructure:"playbackRightNum"`
	ConsolidationExamRightNum               int64  `json:"consolidationExamRightNum" mapstructure:"consolidationExamRightNum"`
	ConsolidationExamFinishNum              int64  `json:"consolidationExamFinishNum" mapstructure:"consolidationExamFinishNum"`
	ConsolidationExamSNum                   int64  `json:"consolidation_exam_s_num" mapstructure:"consolidation_exam_s_num"`
	HomeworkTidFirstRightCnt                int64  `json:"homework_tid_first_right_cnt" mapstructure:"homework_tid_first_right_cnt"`
	WechatReplyCnt                          int64  `json:"wechatReplyCnt" mapstructure:"wechatReplyCnt"`
	PlaybackAllNumNew                       int64  `json:"playback_all_num_new" mapstructure:"playback_all_num_new"`
	NeedAttendLessonNumWithLbp              int64  `json:"need_attend_lesson_num_with_lbp" mapstructure:"need_attend_lesson_num_with_lbp"`
	PlaybackAllNumWithLbp                   int64  `json:"playback_all_num_with_lbp" mapstructure:"playback_all_num_with_lbp"`
	NeedAttendLessonNum                     int64  `json:"needAttendLessonNum" mapstructure:"needAttendLessonNum"`
	AssistantcourseNeedAttendNum            int64  `json:"assistantcourse_need_attend_num" mapstructure:"assistantcourse_need_attend_num"`
	PreviewExamTotalNum                     int64  `json:"previewExamTotalNum" mapstructure:"previewExamTotalNum"`
	AlwaysTestExamTotalNum                  int64  `json:"alwaysTestExamTotalNum" mapstructure:"alwaysTestExamTotalNum"`
	PlaybackAllNum                          int64  `json:"playbackAllNum" mapstructure:"playbackAllNum"`
	ConsolidationExamTotalNum               int64  `json:"consolidationExamTotalNum" mapstructure:"consolidationExamTotalNum"`
	HomeworkTidFirstCorrectCnt              int64  `json:"homework_tid_first_correct_cnt" mapstructure:"homework_tid_first_correct_cnt"`
	WechatCnt                               int64  `json:"wechatCnt" mapstructure:"wechatCnt"`
	SentTotalNum                            int64  `json:"sent_total_num" mapstructure:"sent_total_num"`
	GroupUserMessageSentTotalNum            int64  `json:"groupUserMessageSentTotalNum" mapstructure:"groupUserMessageSentTotalNum"`
	UserSentTotalNum                        int64  `json:"user_sent_total_num" mapstructure:"user_sent_total_num"`
	TotalVoiceChatDuration                  int64  `json:"total_voice_chat_duration" mapstructure:"total_voice_chat_duration"`
	ValidSessionTotalNum                    int64  `json:"validSessionTotalNum" mapstructure:"validSessionTotalNum"`
	TotalVoiceChatNum                       int64  `json:"totalVoiceChatNum" mapstructure:"totalVoiceChatNum"`
	Recent7DaysTianyingValidChatNum         int64  `json:"recent7DaysTianyingValidChatNum" mapstructure:"recent7DaysTianyingValidChatNum"`
	Recent7DaysTianyingValidChatNumHlight   int64  `json:"recent7DaysTianyingValidChatNumHlight" mapstructure:"recent7DaysTianyingValidChatNumHlight"`
	ProvinceName                            string `json:"provinceName" mapstructure:"provinceName"`
	IsBoundDiscount                         int64  `json:"isBoundDiscount" mapstructure:"isBoundDiscount"`
	PreviewExamRightNum                     int64  `json:"previewExamRightNum" mapstructure:"previewExamRightNum"`
	CityLevel                               int64  `json:"cityLevel" mapstructure:"cityLevel"`
	SeriseSeason                            int64  `json:"seriseSeason" mapstructure:"seriseSeason"`
	IsOriginStudent                         int64  `json:"isOriginStudent" mapstructure:"isOriginStudent"`
	UserDelaminationContinueLevel           string `json:"userDelaminationContinueLevel" mapstructure:"userDelaminationContinueLevel"`
	UserBedType                             string `json:"userBedType" mapstructure:"userBedType"`
	IsPreclass                              int64  `json:"isPreclass" mapstructure:"isPreclass"`
	UserSecondSource                        string `json:"userSecondSource" mapstructure:"userSecondSource"`
	LeapUserType                            string `json:"leap_user_type" mapstructure:"leap_user_type"`
	UserBedUserType                         string `json:"userBedUserType" mapstructure:"userBedUserType"`
	SubjectNumSemester                      string `json:"subject_num_semester" mapstructure:"subject_num_semester"`
	AttendLessonRate                        string `json:"attendLessonRate" mapstructure:"attendLessonRate"`
	AttendLessonRateHlight                  int64  `json:"attendLessonRateHlight" mapstructure:"attendLessonRateHlight"`
	FinishLessonRate                        string `json:"finishLessonRate" mapstructure:"finishLessonRate"`
	FinishLessonRateHlight                  int64  `json:"finishLessonRateHlight" mapstructure:"finishLessonRateHlight"`
	PreclassFinishAttendLessonRate          string `json:"preclassFinishAttendLessonRate" mapstructure:"preclassFinishAttendLessonRate"`
	PreclassFinishAttendLessonRateHlight    int64  `json:"preclassFinishAttendLessonRateHlight" mapstructure:"preclassFinishAttendLessonRateHlight"`
	PreviewExamRightRate                    string `json:"previewExamRightRate" mapstructure:"previewExamRightRate"`
	PreviewExamRightRateNum                 int64  `json:"previewExamRightRateNum" mapstructure:"previewExamRightRateNum"`
	InteractiveExamFinshRateNum             int64  `json:"interactiveExamFinshRateNum" mapstructure:"interactiveExamFinshRateNum"`
	InteractiveExamFinshRate                string `json:"interactiveExamFinshRate" mapstructure:"interactiveExamFinshRate"`
	InteractiveExamRightRate                string `json:"interactiveExamRightRate" mapstructure:"interactiveExamRightRate"`
	InteractiveExamRightRateNum             int64  `json:"interactiveExamRightRateNum" mapstructure:"interactiveExamRightRateNum"`
	AlwaysTestExamRightRate                 string `json:"alwaysTestExamRightRate" mapstructure:"alwaysTestExamRightRate"`
	AlwaysTestExamRightRateNum              int64  `json:"alwaysTestExamRightRateNum" mapstructure:"alwaysTestExamRightRateNum"`
	ConsolidationExamRightRate              string `json:"consolidationExamRightRate" mapstructure:"consolidationExamRightRate"`
	ConsolidationExamRightRateNum           int64  `json:"consolidationExamRightRateNum" mapstructure:"consolidationExamRightRateNum"`
	ValidSessionTotalNumSort                int64  `json:"validSessionTotalNumSort" mapstructure:"validSessionTotalNumSort"`
	ValidSessionTotalNumHlight              int64  `json:"validSessionTotalNumHlight" mapstructure:"validSessionTotalNumHlight"`
	InteractiveExamTotalNum                 int64  `json:"interactiveExamTotalNum" mapstructure:"interactiveExamTotalNum"`
	InteractiveExamFinishNum                int64  `json:"interactiveExamFinishNum" mapstructure:"interactiveExamFinishNum"`
	InteractiveExamRightNum                 int64  `json:"interactiveExamRightNum" mapstructure:"interactiveExamRightNum"`
	GroupUserMessageSentTotalNumSort        int64  `json:"groupUserMessageSentTotalNumSort" mapstructure:"groupUserMessageSentTotalNumSort"`
	GroupUserMessageSentTotalNumHlight      int64  `json:"groupUserMessageSentTotalNumHlight" mapstructure:"groupUserMessageSentTotalNumHlight"`
	SameTypePv                              int64  `json:"sameTypePv" mapstructure:"sameTypePv"`
	SameTypeRank                            int64  `json:"sameTypeRank" mapstructure:"sameTypeRank"`
	Lesson1TotalNum                         int64  `json:"lesson1TotalNum" mapstructure:"lesson1TotalNum"`
	Lesson1FinishNum                        int64  `json:"lesson1FinishNum" mapstructure:"lesson1FinishNum"`
	ChallengeTotalNum                       int64  `json:"challengeTotalNum" mapstructure:"challengeTotalNum"`
	ChallengeFinishNum                      int64  `json:"challengeFinishNum" mapstructure:"challengeFinishNum"`
	AttendLongLessonNum14d                  int64  `json:"attend_long_lesson_num_14d" mapstructure:"attend_long_lesson_num_14d"`
	AttendFinishLessonNum14d                int64  `json:"attend_finish_lesson_num_14d" mapstructure:"attend_finish_lesson_num_14d"`
	SubjectNum                              int64  `json:"subject_num" mapstructure:"subject_num"`
	PlaybackParticipateRate                 string `json:"playbackParticipateRate" mapstructure:"playbackParticipateRate"`
	PlaybackRightRate                       string `json:"playbackRightRate" mapstructure:"playbackRightRate"`
	WechatReplyRate                         string `json:"wechatReplyRate" mapstructure:"wechatReplyRate"`
	PreviewExamFinishRate                   string `json:"previewExamFinishRate" mapstructure:"previewExamFinishRate"`
	AlwaysTestExamFinishRate                string `json:"alwaysTestExamFinishRate" mapstructure:"alwaysTestExamFinishRate"`
	UserBedRankRate                         string `json:"user_bed_rank_rate" mapstructure:"user_bed_rank_rate"`
	ConsolidationExamFinishRate             string `json:"consolidationExamFinishRate" mapstructure:"consolidationExamFinishRate"`
	Lesson1FinishRate                       string `json:"lesson1FinishRate" mapstructure:"lesson1FinishRate"`
	ChallengeFinishRate                     string `json:"challengeFinishRate" mapstructure:"challengeFinishRate"`
	AttendLongLessonNumTimelessRate         string `json:"attendLongLessonNumTimelessRate" mapstructure:"attendLongLessonNumTimelessRate"`
	AttendLongLessonNumTimelessRateHlight   int64  `json:"attendLongLessonNumTimelessRateHlight" mapstructure:"attendLongLessonNumTimelessRateHlight"`
	AttendFinishLessonNumTimelessRate       string `json:"attendFinishLessonNumTimelessRate" mapstructure:"attendFinishLessonNumTimelessRate"`
	AttendFinishLessonNumTimelessRateHlight int64  `json:"attendFinishLessonNumTimelessRateHlight" mapstructure:"attendFinishLessonNumTimelessRateHlight"`
	AssistantCourseAttendFinishRate         string `json:"assistantCourseAttendFinishRate" mapstructure:"assistantCourseAttendFinishRate"`
	AssistantCourseAttendRate               string `json:"assistantCourseAttendRate" mapstructure:"assistantCourseAttendRate"`
	AttendLongLessonNum14dRate              string `json:"attendLongLessonNum14dRate" mapstructure:"attendLongLessonNum14dRate"`
	AttendFinishLessonNum14dRate            string `json:"attendFinishLessonNum14dRate" mapstructure:"attendFinishLessonNum14dRate"`
	ConsolidationExamSRate                  string `json:"consolidationExamSRate" mapstructure:"consolidationExamSRate"`
	HomeworkTidFirstRightCntRate            string `json:"homeworkTidFirstRightCntRate" mapstructure:"homeworkTidFirstRightCntRate"`
	UserActiveValidSessionCount             int64  `json:"userActiveValidSessionCount" mapstructure:"userActiveValidSessionCount"`
	InteractiveExamFinshSort                int64  `json:"interactiveExamFinshSort" mapstructure:"interactiveExamFinshSort"`
	InteractiveExamFinshRateHlight          int64  `json:"interactiveExamFinshRateHlight" mapstructure:"interactiveExamFinshRateHlight"`
	InteractiveExamRightSort                int64  `json:"interactiveExamRightSort" mapstructure:"interactiveExamRightSort"`
	InteractiveExamRightRateHlight          int64  `json:"interactiveExamRightRateHlight" mapstructure:"interactiveExamRightRateHlight"`
	AlwaysTestExamRightSort                 int64  `json:"alwaysTestExamRightSort" mapstructure:"alwaysTestExamRightSort"`
	AlwaysTestExamRightRateHlight           int64  `json:"alwaysTestExamRightRateHlight" mapstructure:"alwaysTestExamRightRateHlight"`
	ConsolidationExamRightSort              int64  `json:"consolidationExamRightSort" mapstructure:"consolidationExamRightSort"`
	ConsolidationExamRightRateHlight        int64  `json:"consolidationExamRightRateHlight" mapstructure:"consolidationExamRightRateHlight"`
	PreviewExamRightRateSort                int64  `json:"previewExamRightRateSort" mapstructure:"previewExamRightRateSort"`
	PreviewExamRightRateHlight              int64  `json:"previewExamRightRateHlight" mapstructure:"previewExamRightRateHlight"`
}

type GetLpcListByCourseStudentParam struct {
	CourseId   int64    `form:"courseId"`
	StudentUid int64    `form:"studentUid"`
	Fields     []string `form:"fields"`
}

type GetLpcListByCourseStudentResp struct {
	LpcUid       int64 `json:"lpc_uid" mapstructure:"lpc_uid"`
	CourseId     int64 `json:"course_id" mapstructure:"course_id"`
	StudentUid   int64 `json:"student_uid" mapstructure:"student_uid"`
	LessonId     int64 `json:"lesson_id" mapstructure:"lesson_id"`
	LeadsId      int64 `json:"leads_id" mapstructure:"leads_id"`
	PlaybackTime int64 `json:"playback_time" mapstructure:"playback_time"`
}

type GetListByCourseIdsStudentUidsParam struct {
	CourseIds   []int64  `form:"courseIds"`
	StudentUids []int64  `form:"studentUids"`
	Fields      []string `form:"fields"`
}

type GetListByCourseIdsStudentUidsResp struct {
	LessonId                int64 `json:"lesson_id" mapstructure:"lesson_id"`
	PreviewCorrectNum       int   `json:"previewCorrectNum" mapstructure:"previewCorrectNum"`
	PreviewParticipateNum   int   `json:"previewParticipateNum" mapstructure:"previewParticipateNum"`
	PreviewTotalNum         int   `json:"previewTotalNum" mapstructure:"previewTotalNum"`
	AttendDuration          int64 `json:"attendDuration" mapstructure:"attendDuration"`
	PlaybackTotalTime       int64 `json:"playbackTotalTime" mapstructure:"playbackTotalTime"`
	PlaybackTimeAfterUnlock int64 `json:"playbackTimeAfterUnlock" mapstructure:"playbackTimeAfterUnlock"`
}

type GetLuComonListByStudentLessonsParam struct {
	LessonIds  []int64  `form:"lessonIds"`
	StudentUid int64    `form:"studentUid"`
	Fields     []string `form:"fields"`
}

type GetLuComonListByStudentLessonsResp struct {
	LessonId                                   int64 `json:"lesson_id" mapstructure:"lesson_id"`
	InclassTeacherToomTotalPlaybackContentTime int64 `json:"inclass_teacher_room_total_playback_content_time" mapstructure:"inclass_teacher_room_total_playback_content_time"`
}

// GetIdlLpcLeadsDataResp LPC leads数据响应结构
type GetIdlLpcLeadsDataResp struct {
	List  []interface{} `json:"list"`
	Total int           `json:"total"`
}

// IdlLpcLeadsData LPC leads数据结构
type IdlLpcLeadsData struct {
	LeadsId     int64 `json:"leads_id" mapstructure:"leads_id"`
	StudentUid  int64 `json:"student_uid" mapstructure:"student_uid"`
	CourseId    int64 `json:"course_id" mapstructure:"course_id"`
	PlaybackNum int64 `json:"playback_num" mapstructure:"playback_num"`
	FinishNum   int64 `json:"finish_num" mapstructure:"finish_num"`
}

// CityDataResp 城市信息响应
type CityDataResp struct {
	CityName      string `json:"city_name" mapstructure:"city_name"`
	CityLevel     int64  `json:"city_level" mapstructure:"city_level"`
	CityLevelName string `json:"city_level_name" mapstructure:"city_level_name"`
}
