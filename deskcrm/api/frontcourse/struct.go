package frontcourse

// FrontCourseResponse frontcourse API通用响应结构
type FrontCourseResponse struct {
	ErrNo  int                              `json:"errNo"`
	ErrMsg string                           `json:"errMsg"`
	Data   map[int64]HighGradePreviewInfo   `json:"data"`
}

// HighGradePreviewInfo 初高中预习信息
type HighGradePreviewInfo struct {
	Status int `json:"status"` // 是否有预习 0没有 1有
	IsOpen int `json:"isOpen"` // 是否开启 0未开启 1开启
}
