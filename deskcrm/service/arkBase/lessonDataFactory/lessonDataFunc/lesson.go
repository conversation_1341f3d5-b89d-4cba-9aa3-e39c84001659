package lessonDataFunc

import (
	"deskcrm/api/achilles"
	"deskcrm/api/dal"
	"deskcrm/api/das"
	"deskcrm/api/dataproxy"
	"deskcrm/api/jxexamui"
	"deskcrm/components/define"
	"deskcrm/models"
	"deskcrm/service/arkBase/dataQuery"
	"fmt"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

// GetLessonId 获取章节ID
// 对应PHP中的lessonId字段，直接返回lessonID
func (s *Format) GetLessonId(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceApiLessonReport, []string{}) {
		return
	}

	for _, lessonID := range s.param.LessonIDs {
		// 直接返回lessonID，对应PHP中的 $row['lessonId'] = $val['lessonId']
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, lessonID)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【获取章节ID】", "直接返回lessonID参数")
	return
}

func (s *Format) GetLessonName(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceApiLessonReport, []string{}) {
		return
	}
	queryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetCourseInfo", []interface{}{s.param.CourseID})
	if err != nil {
		return
	}
	courseInfo := queryData.(dal.CourseInfo)
	lessonMap := map[int64]dal.LessonInfo{}
	for _, lessonDetail := range courseInfo.LessonList {
		lessonMap[int64(lessonDetail.LessonId)] = lessonDetail
	}

	for _, lessonID := range s.param.LessonIDs {
		lessonName := ""
		if _, ok := lessonMap[lessonID]; ok {
			lessonName = lessonMap[lessonID].LessonName
		}
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, lessonName)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【获取章节名称】 ", "dal courseinfo")
	return
}

// GetType 获取章节类型
// 对应PHP中的type字段，来源于lessonType
func (s *Format) GetType(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceApiLessonReport, []string{}) {
		return
	}
	queryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetCourseInfo", []interface{}{s.param.CourseID})
	if err != nil {
		return
	}
	courseInfo := queryData.(dal.CourseInfo)
	lessonMap := map[int64]dal.LessonInfo{}
	for _, lessonDetail := range courseInfo.LessonList {
		lessonMap[int64(lessonDetail.LessonId)] = lessonDetail
	}

	for _, lessonID := range s.param.LessonIDs {
		lessonType := 0
		if _, ok := lessonMap[lessonID]; ok {
			lessonType = lessonMap[lessonID].LessonType
		}
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, lessonType)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【获取章节类型】", "dal courseinfo")
	return
}

// GetPlayType 获取播放类型
// 对应PHP中的playType字段
func (s *Format) GetPlayType(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceApiLessonReport, []string{}) {
		return
	}
	queryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetCourseInfo", []interface{}{s.param.CourseID})
	if err != nil {
		return
	}
	courseInfo := queryData.(dal.CourseInfo)
	lessonMap := map[int64]dal.LessonInfo{}
	for _, lessonDetail := range courseInfo.LessonList {
		lessonMap[int64(lessonDetail.LessonId)] = lessonDetail
	}

	for _, lessonID := range s.param.LessonIDs {
		playType := 0
		if _, ok := lessonMap[lessonID]; ok {
			playType = lessonMap[lessonID].PlayType
		}
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, playType)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【获取播放类型】", "dal courseinfo")
	return
}

// GetInclassTime 获取上课时间
// 对应PHP中的inclassTime字段，格式化为"YYYY-MM-DD HH:MM-HH:MM"
func (s *Format) GetInclassTime(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceApiLessonReport, []string{}) {
		return
	}
	queryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetCourseInfo", []interface{}{s.param.CourseID})
	if err != nil {
		return
	}
	courseInfo := queryData.(dal.CourseInfo)
	lessonMap := map[int64]dal.LessonInfo{}
	for _, lessonDetail := range courseInfo.LessonList {
		lessonMap[int64(lessonDetail.LessonId)] = lessonDetail
	}

	for _, lessonID := range s.param.LessonIDs {
		inclassTime := ""
		if _, ok := lessonMap[lessonID]; ok {
			startTime := lessonMap[lessonID].StartTime
			stopTime := lessonMap[lessonID].StopTime
			// 格式化为 "2006-01-02 15:04-15:04" 格式，对应PHP的LESSON_INCLASS_TIME
			startTimeStr := time.Unix(int64(startTime), 0).Format("2006-01-02 15:04")
			stopTimeStr := time.Unix(int64(stopTime), 0).Format("15:04")
			inclassTime = startTimeStr + "-" + stopTimeStr
		}
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, inclassTime)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【获取上课时间】", "dal courseinfo")
	return
}

// GetStopTime 获取章节结束时间
// 对应PHP中的stopTime字段
func (s *Format) GetStopTime(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceApiLessonReport, []string{}) {
		return
	}
	queryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetCourseInfo", []interface{}{s.param.CourseID})
	if err != nil {
		return
	}
	courseInfo := queryData.(dal.CourseInfo)
	lessonMap := map[int64]dal.LessonInfo{}
	for _, lessonDetail := range courseInfo.LessonList {
		lessonMap[int64(lessonDetail.LessonId)] = lessonDetail
	}

	for _, lessonID := range s.param.LessonIDs {
		stopTime := 0
		if _, ok := lessonMap[lessonID]; ok {
			stopTime = lessonMap[lessonID].StopTime
		}
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, stopTime)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【获取章节结束时间】", "dal courseinfo")
	return
}

// GetStartTime 获取章节开始时间
// 对应PHP中的startTime字段
func (s *Format) GetStartTime(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceApiLessonReport, []string{}) {
		return
	}
	queryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetCourseInfo", []interface{}{s.param.CourseID})
	if err != nil {
		return
	}
	courseInfo := queryData.(dal.CourseInfo)
	lessonMap := map[int64]dal.LessonInfo{}
	for _, lessonDetail := range courseInfo.LessonList {
		lessonMap[int64(lessonDetail.LessonId)] = lessonDetail
	}

	for _, lessonID := range s.param.LessonIDs {
		startTime := 0
		if _, ok := lessonMap[lessonID]; ok {
			startTime = lessonMap[lessonID].StartTime
		}
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, startTime)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【获取章节开始时间】", "dal courseinfo")
	return
}

// GetPreview 获取预习数据
// 对应PHP中的preview字段，格式化为"X/Y/Z"（正确数/回答数/总数）
// 包含iLab兼容逻辑和预习开启状态检查
func (s *Format) GetPreview(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceCommonLu, []string{}) {
		return
	}

	// 获取基础LU数据
	queryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLuData", []interface{}{
		[]int64{s.param.CourseID},
		[]int64{s.param.StudentUid},
		[]string{"previewTotalNum", "previewCorrectNum", "previewParticipateNum"},
	})
	if err != nil {
		return
	}

	luData := queryData.(map[string]*dataproxy.GetListByCourseIdsStudentUidsResp)

	// 获取课程信息
	courseInfo, err := s.dataQueryPoint.GetInstanceData(ctx, "GetCourseInfo", []interface{}{s.param.CourseID})
	if err != nil {
		return
	}
	courseData := courseInfo.(dal.CourseInfo)
	gradeId := int(courseData.MainGradeId)
	subjectId := int(courseData.MainSubjectId)

	// 获取学段信息
	gradeStage := define.Grade2XB[gradeId]

	// 获取iLab信息（仅针对初二物理）
	ilabInfo, err := s.dataQueryPoint.GetInstanceData(ctx, "GetILabInfo", []interface{}{
		gradeId, subjectId, s.param.LessonIDs, s.param.StudentUid,
	})
	if err != nil {
		return
	}
	ilabData := ilabInfo.(*dataQuery.ILabInfo)

	// 获取预习开启状态信息
	previewOpenInfo, err := s.dataQueryPoint.GetInstanceData(ctx, "GetPreviewOpenInfo", []interface{}{
		s.param.CourseID, s.param.LessonIDs, nil, gradeStage,
	})
	if err != nil {
		return
	}
	previewOpenData := previewOpenInfo.(map[int64]dataQuery.PreviewOpenInfo)

	for _, lessonID := range s.param.LessonIDs {
		previewData := ""
		key := fmt.Sprintf("%d_%d_%d", s.param.CourseID, lessonID, s.param.StudentUid)

		if lessonLuData, ok := luData[key]; ok {
			correctNum := lessonLuData.PreviewCorrectNum
			participateNum := lessonLuData.PreviewParticipateNum
			totalNum := lessonLuData.PreviewTotalNum

			// 基础预习数据格式化
			if totalNum == 0 {
				previewData = "-"
			} else {
				previewData = fmt.Sprintf("%d/%d/%d", correctNum, participateNum, totalNum)
			}

			// iLab兼容逻辑 - 针对初二物理课程
			if gradeId == 3 && subjectId == 4 {
				if checkInfo, exists := ilabData.CheckIlabLesson[lessonID]; exists && checkInfo.ILabLesson {
					if level, hasLevel := ilabData.PreviewInfoByIlab[lessonID]; hasLevel {
						if levelText, ok := jxexamui.LevelIlabMap[level]; ok && levelText != "" {
							previewData = levelText
						} else {
							previewData = "-"
						}
					}
				}
			}

			// 预习开启状态检查
			if previewData == "-" {
				if openInfo, exists := previewOpenData[lessonID]; exists {
					if openInfo.IsOpenPreview == 1 {
						// 预习已开启但没有数据，显示"未提交"
						previewData = "未提交"
					} else {
						// 预习未开启，显示"-"
						previewData = "-"
					}
				}
			}
		} else {
			// 没有LU数据时，检查预习开启状态
			previewData = "-"
			if openInfo, exists := previewOpenData[lessonID]; exists {
				if openInfo.IsOpenPreview == 1 {
					previewData = "未提交"
				}
			}
		}

		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, previewData)
	}

	_ = s.AddDataSource(ctx, s.rule.Key, "【获取预习数据】", "CommonLU: previewCorrectNum, previewParticipateNum, previewTotalNum; iLab兼容; 预习开启状态检查")
	return
}

// GetAttendData 获取到课数据
// 对应PHP中的attend字段，格式化为"XminYs"
func (s *Format) GetAttendData(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceLu, []string{}) {
		return
	}

	// 获取学生课程数据 - attendDuration字段
	queryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLuData", []interface{}{
		[]int64{s.param.CourseID},
		[]int64{s.param.StudentUid},
		[]string{"attendDuration"},
	})
	if err != nil {
		return
	}

	// 获取课程信息 - playType字段
	courseData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetCourseInfo", []interface{}{s.param.CourseID})
	if err != nil {
		return
	}

	// 获取学生章节请假信息 - 从DB获取
	lessonStudentData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLessonStudentData", []interface{}{s.param.CourseID, s.param.LessonIDs, []int64{s.param.StudentUid}})
	if err != nil {
		return
	}

	studentLessonData := queryData.(map[string]map[string]interface{})
	courseInfo := courseData.(dal.CourseInfo)
	studentLeaveData := lessonStudentData.(map[string]*models.LessonStudent)

	// 构建章节信息映射
	lessonMap := map[int64]dal.LessonInfo{}
	for lessonIDStr, lessonInfo := range courseInfo.LessonList {
		lessonID, _ := strconv.ParseInt(lessonIDStr, 10, 64)
		lessonMap[lessonID] = lessonInfo
	}

	// 请假状态常量
	const ATTEND_STATUS_LEAVE = 3 // 请假状态码

	for _, lessonID := range s.param.LessonIDs {
		attendData := ""
		attendCode := 0
		leaveSeason := ""

		key := fmt.Sprintf("%d_%d_%d", s.param.CourseID, lessonID, s.param.StudentUid)

		// 获取attendDuration
		attendDuration := int64(0)
		if lessonData, ok := studentLessonData[key]; ok {
			if duration, exists := lessonData["attendDuration"]; exists {
				if val, ok := duration.(int64); ok {
					attendDuration = val
				}
			}
		}

		// 获取playType
		playType := int64(0)
		if lesson, ok := lessonMap[lessonID]; ok {
			playType = int64(lesson.PlayType)
		}

		// LBP章节特殊处理 - 显示为"-"，状态码为1
		if playType == dal.PLAY_TYPE_LUBOKE {
			attendData = "-"
			attendDuration = 0
			attendCode = 1 // LBP章节默认算到课
		} else {
			// 格式化到课时长
			if attendDuration > 0 {
				attendData = FormatDuration(attendDuration)
			}

			// 到课状态判断逻辑
			if attendDuration == 0 {
				// 检查是否请假 - 从数据库获取请假信息
				isLeave := false
				leaveReason := ""
				leaveSeasonText := ""

				// 构建学生章节数据的键
				studentKey := models.GetLessonStudentKey(lessonID, s.param.StudentUid)
				if studentInfo, ok := studentLeaveData[studentKey]; ok {
					// 检查预到课状态是否为请假
					if studentInfo.PreAttend == models.PreAttendLeave {
						isLeave = true
						// 构建请假显示文本
						if studentInfo.ExtData.FirstLeaveReason != "" {
							leaveReason = fmt.Sprintf("请假:#%s#", studentInfo.ExtData.FirstLeaveReason)
						} else {
							leaveReason = "请假"
						}
						if studentInfo.ExtData.LeaveSeason != "" {
							leaveReason += studentInfo.ExtData.LeaveSeason
						}
						leaveSeasonText = studentInfo.ExtData.LeaveSeason
					}
				}

				if isLeave {
					attendCode = ATTEND_STATUS_LEAVE // 请假
					attendData = leaveReason
					leaveSeason = leaveSeasonText
				} else {
					attendCode = 0 // 未到
					attendData = "未到"
				}
			} else if attendDuration >= 30*60 { // 30分钟
				attendCode = 1 // 到课时长足够30min
			} else {
				attendCode = 2 // 到课时长不足30min
			}
		}

		// 添加到课数据
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, attendData)

		// 添加到课状态码和请假原因
		_ = s.AddOutputStudent(ctx, lessonID, "attendCode", attendCode)
		_ = s.AddOutputStudent(ctx, lessonID, "leaveSeason", leaveSeason)
	}

	_ = s.AddDataSource(ctx, s.rule.Key, "【直播到课时长】", "ES: attendDuration, DAL: playType, DB: tblLessonStudent学生请假信息")
	return
}

// GetPlayback 获取回放数据
// 对应PHP中的playback字段，格式化为"XminYs"或"-"（录播课程）
func (s *Format) GetPlayback(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceLu, []string{}) {
		return
	}

	// 获取LU数据 - 回放时长相关字段
	luData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLuData", []interface{}{
		[]int64{s.param.CourseID},
		[]int64{s.param.StudentUid},
		[]string{"playbackTotalTime", "playbackTimeAfterUnlock"},
	})
	if err != nil {
		return
	}

	// 获取公共LU数据 - 新回放时长字段
	commonLuData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetCommonLuData", []interface{}{
		s.param.LessonIDs,
		s.param.StudentUid,
		[]string{"inclass_teacher_room_total_playback_time_v1"},
	})
	if err != nil {
		return
	}

	// 获取章节基础信息 - t007Tag和playType字段
	lessonBaseInfo, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLessonBaseInfo", []interface{}{
		s.param.LessonIDs,
		[]string{"t007Tag", "playType"},
	})
	if err != nil {
		return
	}

	luDataMap := luData.(map[int64]*dataproxy.GetListByCourseIdsStudentUidsResp)
	commonLuDataMap := commonLuData.(map[int64]*dataproxy.GetLuComonListByStudentLessonsResp)
	lessonInfoMap := lessonBaseInfo.(map[int64]*achilles.ProcessedLessonInfo)

	for _, lessonID := range s.param.LessonIDs {
		playbackData := "-"
		playbackV1Data := "-"

		// 获取LU数据
		if lessonLuData, ok := luDataMap[lessonID]; ok {
			var playbackTotalTime int64

			// 根据t007Tag决定使用哪个回放时长
			if lessonInfo, exists := lessonInfoMap[lessonID]; exists && lessonInfo.T007Tag == 1 {
				playbackTotalTime = lessonLuData.PlaybackTimeAfterUnlock
			} else {
				playbackTotalTime = lessonLuData.PlaybackTotalTime
			}

			// 检查是否为录播课程
			if lessonInfo, exists := lessonInfoMap[lessonID]; exists && lessonInfo.PlayType == dal.PLAY_TYPE_LUBOKE {
				playbackData = "-"
			} else {
				// 格式化回放时长
				playbackData = s.formatPlaybackTime(playbackTotalTime)
			}
		}

		// 获取新回放时长数据
		if commonLuInfo, ok := commonLuDataMap[lessonID]; ok {
			playbackV1Time := commonLuInfo.InclassTeacherToomTotalPlaybackContentTime
			playbackV1Data = s.formatDurationTime(playbackV1Time)
		}

		// 输出回放时长
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, playbackData)

		// 输出新回放时长（playbackV1）
		_ = s.AddOutputStudent(ctx, lessonID, "playbackV1", playbackV1Data)
	}

	_ = s.AddDataSource(ctx, s.rule.Key, "【回放时长】", "LU: playbackTotalTime, playbackTimeAfterUnlock + CommonLU: inclass_teacher_room_total_playback_time_v1 + Achilles: t007Tag, playType")
	return
}

// GetPlaybackOnlineTimeV1 获取回放观看时长(新)
// 对应PHP中的playbackv1字段，格式化为"XminYs"
func (s *Format) GetPlaybackOnlineTimeV1(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceLu, []string{}) {
		return
	}

	// 获取学生课程数据
	queryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLuData", []interface{}{
		[]int64{s.param.CourseID},
		[]int64{s.param.StudentUid},
		[]string{"inclass_teacher_room_total_playback_time_v1"},
	})
	if err != nil {
		return
	}

	studentLessonData := queryData.(map[string]map[string]interface{})

	for _, lessonID := range s.param.LessonIDs {
		playbackV1Data := ""
		key := fmt.Sprintf("%d_%d_%d", s.param.CourseID, lessonID, s.param.StudentUid)

		if lessonData, ok := studentLessonData[key]; ok {
			if playbackTime, exists := lessonData["inclass_teacher_room_total_playback_time_v1"]; exists {
				if timeVal, ok := playbackTime.(int64); ok && timeVal > 0 {
					playbackV1Data = FormatDuration(timeVal)
				}
			}
		}

		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, playbackV1Data)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【获取回放观看时长(新)】", "ES: inclass_teacher_room_total_playback_time_v1")
	return
}

// GetLbpAttendDuration 获取LBP观看时长
// 对应PHP中的lbpAttendDuration字段，格式化为"XminYs"
func (s *Format) GetLbpAttendDuration(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceCommonLu, []string{}) {
		return
	}

	// 获取公共LU数据
	queryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetCommonLuData", []interface{}{
		s.param.LessonIDs,
		s.param.StudentUid,
		[]string{"inclass_teacher_room_total_playback_content_time"},
	})
	if err != nil {
		return
	}

	commonLuData := queryData.(map[int64]*dataproxy.GetLuComonListByStudentLessonsResp)

	for _, lessonID := range s.param.LessonIDs {
		lbpAttendDuration := ""
		if lessonData, ok := commonLuData[lessonID]; ok {
			lbpAttendDuration = FormatDuration(lessonData.InclassTeacherToomTotalPlaybackContentTime)
		}

		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, lbpAttendDuration)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【获取LBP观看时长】", "CommonLU: inclass_teacher_room_total_playback_content_time")
	return
}

// GetLbpAttendDurationOld 获取LBP观看时长(旧版)
// 对应PHP中的lbpAttendDurationOld字段，格式化为"XminYs"
func (s *Format) GetLbpAttendDurationOld(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceLu, []string{}) {
		return
	}

	// 获取学生课程数据
	queryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLuData", []interface{}{
		[]int64{s.param.CourseID},
		[]int64{s.param.StudentUid},
		[]string{"lbp_attend_duration"},
	})
	if err != nil {
		return
	}

	studentLessonData := queryData.(map[string]map[string]interface{})

	for _, lessonID := range s.param.LessonIDs {
		lbpAttendDurationOld := ""
		key := fmt.Sprintf("%d_%d_%d", s.param.CourseID, lessonID, s.param.StudentUid)

		if lessonData, ok := studentLessonData[key]; ok {
			if attendTime, exists := lessonData["lbp_attend_duration"]; exists {
				if timeVal, ok := attendTime.(int64); ok && timeVal > 0 {
					lbpAttendDurationOld = FormatDuration(timeVal)
				}
			}
		}

		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, lbpAttendDurationOld)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【获取LBP观看时长(旧版)】", "ES: lbp_attend_duration")
	return
}

// GetOralQuestion 获取口述题数据
// 对应PHP中的oralQuestion字段，包含复杂的状态判断逻辑
func (s *Format) GetOralQuestion(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceLu, []string{}) {
		return
	}

	// 获取试卷绑定数据
	bindIds := []string{fmt.Sprintf("%d", s.param.CourseID), fmt.Sprintf("%d", s.param.CourseID)}
	relationTypes := []int{dataQuery.RelationTypeCourse, dataQuery.RelationTypeLesson}

	examData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetExamRelationData", []interface{}{
		bindIds,
		dataQuery.BindTypeOralQuestion,
		relationTypes,
		[]string{"bind_id", "relation_type", "total_num"},
	})
	if err != nil {
		return
	}

	examRelationData := examData.(map[string]interface{})

	for _, lessonID := range s.param.LessonIDs {
		oralQuestionData := ""

		// 检查是否有口述题绑定
		courseBindId := fmt.Sprintf("%d", s.param.CourseID)
		lessonBindId := fmt.Sprintf("%d", lessonID)

		hasCourseBind := false
		hasLessonBind := false

		if _, exists := examRelationData[courseBindId]; exists {
			hasCourseBind = true
		}

		if _, exists := examRelationData[lessonBindId]; exists {
			hasLessonBind = true
		}

		if hasCourseBind || hasLessonBind {
			// 获取学生具体的口述题完成状态
			// 这里需要根据实际业务逻辑来判断状态
			// PHP中使用了AssistantDesk_ServiceTools::getOralQuestionFinishStatus方法
			// 暂时返回一个基础状态
			oralQuestionData = "待完成"
		}

		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, oralQuestionData)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【获取口述题数据】", "试卷绑定 + 学生状态数据")
	return
}

// GetInclassTest 获取堂堂测数据
// 对应PHP中的inclassTest字段，格式化为"X/Y/Z"（正确数/参与数/总数）或小英课程得分
func (s *Format) GetInclassTest(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceApiLessonReport, []string{}) {
		return
	}

	for _, lessonID := range s.param.LessonIDs {
		inclassTestData := ""

		// 获取公共LU数据 - 堂堂测相关字段
		luQueryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLuData", []interface{}{
			s.param.CourseID,
			[]int64{s.param.StudentUid},
			[]string{
				"tangTangExamCorrectNum",
				"tangTangExamParticipateNum",
				"tangTangExamScore",
				"isTangTangExamSubmit",
			},
		})
		if err != nil {
			_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, inclassTestData)
			continue
		}

		// 获取考试绑定数据 - 堂堂测总数
		examData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetExamRelationData", []interface{}{
			[]string{fmt.Sprintf("%d", lessonID)},
			dataQuery.BindTypeTestInClass,
			[]int{dataQuery.RelationTypeLesson},
			[]string{"bind_id", "relation_type", "total_num"},
		})
		if err != nil {
			_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, inclassTestData)
			continue
		}

		commonLuData := luQueryData.(map[string]map[string]interface{})
		examRelationData := examData.(map[string]interface{})

		// 检查是否是小英课程
		isHxCourse := false
		// TODO: 需要实现checkIsHx方法，这里暂时使用默认值
		// isHxCourse = checkIsHx(s.param.CourseID)

		// 获取堂堂测总数
		tangTangTotalNum := int64(0)
		if examInfo, exists := examRelationData[fmt.Sprintf("%d", lessonID)]; exists {
			if examMap, ok := examInfo.(map[string]interface{}); ok {
				if totalNum, exists := examMap["total_num"]; exists {
					if totalVal, ok := totalNum.(int64); ok && totalVal > 0 {
						tangTangTotalNum = totalVal
					}
				}
			}
		}

		// 获取学生堂堂测数据
		luKey := fmt.Sprintf("%d_%d_%d", s.param.CourseID, lessonID, s.param.StudentUid)
		if lessonLuData, exists := commonLuData[luKey]; exists {
			correctNum := int64(0)
			participateNum := int64(0)
			score := int64(0)
			isSubmit := false

			// 获取堂堂测数据
			if correctVal, exists := lessonLuData["tangTangExamCorrectNum"]; exists {
				if val, ok := correctVal.(int64); ok {
					correctNum = val
				}
			}

			if participateVal, exists := lessonLuData["tangTangExamParticipateNum"]; exists {
				if val, ok := participateVal.(int64); ok {
					participateNum = val
				}
			}

			if scoreVal, exists := lessonLuData["tangTangExamScore"]; exists {
				if val, ok := scoreVal.(int64); ok {
					score = val
				}
			}

			if submitVal, exists := lessonLuData["isTangTangExamSubmit"]; exists {
				if val, ok := submitVal.(int64); ok && val > 0 {
					isSubmit = true
				}
			}

			// 根据课程类型显示不同格式
			if !isHxCourse {
				// 非小英课程：显示 "X/Y/Z" 格式，对应PHP的sprintf(self::LESSON_EXERCISE_DETAIL, $tangTangExamCorrectNum, $tangTangExamParticipateNum, $tangTangToalNum)
				if tangTangTotalNum > 0 {
					inclassTestData = fmt.Sprintf("%d/%d/%d", correctNum, participateNum, tangTangTotalNum)
				} else {
					inclassTestData = "-"
				}
			} else {
				// 小英课程：展示得分，对应PHP的sprintf('%s分', ($esLessonData[self::$studentUid]['tangTangExamScore'] / 10))
				if isSubmit {
					inclassTestData = fmt.Sprintf("%d分", score/10)
				} else {
					inclassTestData = "未提交"
				}
			}
		} else {
			if !isHxCourse {
				inclassTestData = "-"
			} else {
				inclassTestData = "未提交"
			}
		}

		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, inclassTestData)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【获取堂堂测数据】", "LU: tangTangExamCorrectNum, tangTangExamParticipateNum, tangTangExamScore, isTangTangExamSubmit + Exam: total_num")
	return
}

// GetHomeworkData 获取作业数据
// 对应PHP中的homework字段，格式化为["状态", "颜色", "是否可点击"]
func (s *Format) GetHomeworkData(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceApiLessonReport, []string{}) {
		return
	}

	for _, lessonID := range s.param.LessonIDs {
		homeworkData := ""

		// 获取DAS数据 - 作业状态和订正状态
		dasQueryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetDasLessonData", []interface{}{[]int64{s.param.StudentUid}, lessonID})
		if err != nil {
			_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, homeworkData)
			continue
		}

		// 获取LU数据 - 作业等级
		luQueryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLuData", []interface{}{
			s.param.CourseID,
			[]int64{s.param.StudentUid},
			[]string{"homeworkLevel", "homeworkPracticeCorrectNum", "homeworkPracticeParticipateNum"},
		})
		if err != nil {
			_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, homeworkData)
			continue
		}

		// 获取作业绑定数据
		examData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetExamRelationData", []interface{}{
			[]string{fmt.Sprintf("%d", lessonID)},
			dataQuery.BindTypeHomework,
			[]int{dataQuery.RelationTypeLesson},
			[]string{"bind_id", "relation_type", "total_num"},
		})
		if err != nil {
			_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, homeworkData)
			continue
		}

		studentLessonMap := dasQueryData.(map[int64]map[int64]*das.StudentLessonInfo)
		commonLuData := luQueryData.(map[string]map[string]interface{})
		examRelationData := examData.(map[string]interface{})

		// 检查是否绑定作业
		isBindHw := false
		if _, exists := examRelationData[fmt.Sprintf("%d", lessonID)]; exists {
			isBindHw = true
		}

		// 获取学生作业信息
		studentLessonInfo, hasStudentData := studentLessonMap[s.param.StudentUid][lessonID]
		luKey := fmt.Sprintf("%d_%d_%d", s.param.CourseID, lessonID, s.param.StudentUid)
		lessonLuData, hasLuData := commonLuData[luKey]

		// 作业状态判断逻辑
		if !hasStudentData {
			homeworkData = "-"
		} else if !isBindHw {
			homeworkData = "未布置"
		} else if studentLessonInfo.HomeworkStatus == 1 {
			homeworkData = "未提交"
		} else if studentLessonInfo.HomeworkStatus == 2 {
			homeworkData = "未批改"
		} else if studentLessonInfo.HomeworkStatus == 3 {
			// 已批改，显示等级
			if hasLuData {
				if homeworkLevel, exists := lessonLuData["homeworkLevel"]; exists {
					if levelVal, ok := homeworkLevel.(int64); ok && levelVal > 0 {
						homeworkData = GetHomeworkLevelString(levelVal)
					} else {
						homeworkData = "暂无等级"
					}
				} else {
					homeworkData = "暂无等级"
				}
			} else {
				homeworkData = "暂无等级"
			}
		} else {
			homeworkData = "-"
		}

		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, homeworkData)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【获取作业数据】", "DAS: homeworkStatus + LU: homeworkLevel + 作业绑定数据")
	return
}

// GetHomeworkLikeData 获取相似题作业数据
// 对应PHP中的similarHomework字段，格式化为[状态, 颜色, 是否可点击]
func (s *Format) GetHomeworkLikeData(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceLu, []string{}) {
		return
	}

	// 获取学生课程数据 - 相似题相关字段
	queryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLuData", []interface{}{
		[]int64{s.param.CourseID},
		[]int64{s.param.StudentUid},
		[]string{"exam_answer.exam33.correct_level", "exam_answer.exam33.last_correct_status", "exam_answer.exam33.submit_num"},
	})
	if err != nil {
		return
	}

	// 获取作业绑定数据
	examData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetExamRelationData", []interface{}{
		[]string{fmt.Sprintf("%d", s.param.CourseID)},
		dataQuery.BindTypeHomeworkSimilars,
		[]int{dataQuery.RelationTypeCourse},
		[]string{"bind_id", "relation_type", "total_num"},
	})
	if err != nil {
		return
	}

	studentLessonData := queryData.(map[string]map[string]interface{})
	examRelationData := examData.(map[string]interface{})

	// 检查是否绑定相似题作业
	isBindSimilarHw := false
	if _, exists := examRelationData[fmt.Sprintf("%d", s.param.CourseID)]; exists {
		isBindSimilarHw = true
	}

	for _, lessonID := range s.param.LessonIDs {
		similarHomeworkData := ""
		key := fmt.Sprintf("%d_%d_%d", s.param.CourseID, lessonID, s.param.StudentUid)

		if lessonData, ok := studentLessonData[key]; ok {
			correctLevel := int64(0)
			correctStatus := int64(0)

			// 获取相似题正确等级和状态
			if examAnswer, exists := lessonData["exam_answer.exam33.correct_level"]; exists {
				if levelVal, ok := examAnswer.(int64); ok {
					correctLevel = levelVal
				}
			}

			if examAnswer, exists := lessonData["exam_answer.exam33.last_correct_status"]; exists {
				if statusVal, ok := examAnswer.(int64); ok {
					correctStatus = statusVal
				}
			}

			// 相似题作业状态判断逻辑
			if isBindSimilarHw {
				// 这里需要根据correctStatus判断是否在等待显示状态
				// 暂时使用简化逻辑，实际需要参考AssistantDesk_Config::EXAM_CORRECT_STATUS_WAIT_SHOW_MAP
				if correctStatus > 0 {
					// 等待显示状态
					similarHomeworkData = "待显示"
				} else {
					// 显示等级
					similarHomeworkData = GetHomeworkLevelString(correctLevel)
				}
			} else {
				similarHomeworkData = "-"
			}
		} else {
			similarHomeworkData = "-"
		}

		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, similarHomeworkData)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【获取相似题作业数据】", "ES: exam_answer.exam33.correct_level + 作业绑定数据")
	return
}

// GetExerciseColumn 获取互动题数据
// 对应PHP中的exercise字段，格式化为"X/Y/Z"（正确数/回答数/总数）
func (s *Format) GetExerciseColumn(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceApiLessonReport, []string{}) {
		return
	}

	for _, lessonID := range s.param.LessonIDs {
		exerciseData := ""

		// 获取公共LU数据 - 互动题相关字段
		luQueryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLuData", []interface{}{
			s.param.CourseID,
			[]int64{s.param.StudentUid},
			[]string{"inclass_right_cnt", "inclass_participate_cnt", "inclass_question_cnt"},
		})
		if err != nil {
			_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, exerciseData)
			continue
		}

		// 获取DAS数据 - 到课状态
		dasQueryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetDasLessonData", []interface{}{[]int64{s.param.StudentUid}, lessonID})
		if err != nil {
			_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, exerciseData)
			continue
		}

		// 获取考试绑定数据 - 互动题总数
		examData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetExamRelationData", []interface{}{
			[]string{fmt.Sprintf("%d", lessonID)},
			dataQuery.BindTypePracticeInClass,
			[]int{dataQuery.RelationTypeLesson},
			[]string{"bind_id", "relation_type", "total_num"},
		})
		if err != nil {
			_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, exerciseData)
			continue
		}

		commonLuData := luQueryData.(map[string]map[string]interface{})
		studentLessonMap := dasQueryData.(map[int64]map[int64]*das.StudentLessonInfo)
		examRelationData := examData.(map[string]interface{})

		// 检查灰度 - INACT_TOTAL_NUM_TANS_GRAY_KEY
		// TODO: 需要实现灰度检查逻辑，这里暂时默认使用灰度逻辑
		isGrayHit := true // 默认命中灰度

		// 获取互动题总数
		totalCnt := int64(0)
		if isGrayHit {
			// 灰度命中：使用 inclass_question_cnt
			luKey := fmt.Sprintf("%d_%d_%d", s.param.CourseID, lessonID, s.param.StudentUid)
			if lessonLuData, exists := commonLuData[luKey]; exists {
				if questionCntVal, exists := lessonLuData["inclass_question_cnt"]; exists {
					if val, ok := questionCntVal.(int64); ok {
						totalCnt = val
					}
				}
			}
		} else {
			// 非灰度：使用考试绑定数据
			if examInfo, exists := examRelationData[fmt.Sprintf("%d", lessonID)]; exists {
				if examMap, ok := examInfo.(map[string]interface{}); ok {
					if totalNum, exists := examMap["total_num"]; exists {
						if totalVal, ok := totalNum.(int64); ok && totalVal > 0 {
							totalCnt = totalVal
						}
					}
				}
			}
		}

		// 获取学生互动题数据
		luKey := fmt.Sprintf("%d_%d_%d", s.param.CourseID, lessonID, s.param.StudentUid)
		if lessonLuData, exists := commonLuData[luKey]; exists {
			rightCnt := int64(0)
			participateCnt := int64(0)

			// 获取互动题正确数和参与数
			if rightCntVal, exists := lessonLuData["inclass_right_cnt"]; exists {
				if val, ok := rightCntVal.(int64); ok {
					rightCnt = val
				}
			}

			if participateCntVal, exists := lessonLuData["inclass_participate_cnt"]; exists {
				if val, ok := participateCntVal.(int64); ok {
					participateCnt = val
				}
			}

			// 获取DAS到课状态（用于状态码逻辑）
			_ = false // 预留变量，如果需要状态码逻辑可以使用
			if studentLessonInfo, ok := studentLessonMap[s.param.StudentUid][lessonID]; ok {
				_ = studentLessonInfo.AttendedDuration > 0
			}

			// 如果总数为0，则显示"-"
			if totalCnt == 0 {
				exerciseData = "-"
			} else {
				// 格式化为 "X/Y/Z" 格式，对应PHP的sprintf(self::LESSON_EXERCISE_DETAIL, $inclass_right_cnt, $inclass_participate_cnt, $intInteractTotalNum)
				exerciseData = fmt.Sprintf("%d/%d/%d", rightCnt, participateCnt, totalCnt)
			}

			// TODO: 设置exerciseCode状态码逻辑（如果需要）
			// if !isAttended {
			//     exerciseCode = 0 // 未参与
			// } else if totalCnt > 0 && (rightCnt == totalCnt) {
			//     exerciseCode = 1 // 全答全对
			// } else {
			//     exerciseCode = 2 // 未全答全对
			// }
		} else {
			exerciseData = "-"
		}

		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, exerciseData)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【获取互动题数据】", "LU: inclass_right_cnt, inclass_participate_cnt, inclass_question_cnt + DAS: isAttended + Exam: total_num")
	return
}

// GetExerciseAllColumn 获取观看互动题数据
// 对应PHP中的exerciseAll字段，格式化为"X/Y/Z"（正确数/回答数/总数）
func (s *Format) GetExerciseAllColumn(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceCommonLu, []string{}) {
		return
	}

	// 获取公共LU数据 - 观看互动题相关字段
	queryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLuData", []interface{}{
		s.param.CourseID,
		[]int64{s.param.StudentUid},
		[]string{
			"inclass_right_cnt",
			"inclass_participate_cnt",
			"playback_right_cnt",
			"playback_participate_cnt",
			"inclass_question_cnt",
		},
	})
	if err != nil {
		return
	}

	commonLuData := queryData.(map[string]map[string]interface{})

	for _, lessonID := range s.param.LessonIDs {
		exerciseAllData := ""
		key := fmt.Sprintf("%d_%d_%d", s.param.CourseID, lessonID, s.param.StudentUid)

		if lessonLuData, ok := commonLuData[key]; ok {
			inclassRightCnt := int64(0)
			inclassParticipateCnt := int64(0)
			playbackRightCnt := int64(0)
			playbackParticipateCnt := int64(0)
			totalCnt := int64(0)

			// 获取课中互动题数据
			if rightCntVal, exists := lessonLuData["inclass_right_cnt"]; exists {
				if val, ok := rightCntVal.(int64); ok {
					inclassRightCnt = val
				}
			}

			if participateCntVal, exists := lessonLuData["inclass_participate_cnt"]; exists {
				if val, ok := participateCntVal.(int64); ok {
					inclassParticipateCnt = val
				}
			}

			// 获取回放互动题数据
			if playbackRightVal, exists := lessonLuData["playback_right_cnt"]; exists {
				if val, ok := playbackRightVal.(int64); ok {
					playbackRightCnt = val
				}
			}

			if playbackParticipateVal, exists := lessonLuData["playback_participate_cnt"]; exists {
				if val, ok := playbackParticipateVal.(int64); ok {
					playbackParticipateCnt = val
				}
			}

			// 获取互动题总数 - 支持灰度逻辑
			if questionCntVal, exists := lessonLuData["inclass_question_cnt"]; exists {
				if val, ok := questionCntVal.(int64); ok {
					totalCnt = val
				}
			}

			// 如果总数为0，则显示"-"
			if totalCnt == 0 {
				exerciseAllData = "-"
			} else {
				// 计算总正确数和总参与数
				totalRightCnt := inclassRightCnt + playbackRightCnt
				totalParticipateCnt := inclassParticipateCnt + playbackParticipateCnt
				// 格式化为 "X/Y/Z" 格式
				exerciseAllData = fmt.Sprintf("%d/%d/%d", totalRightCnt, totalParticipateCnt, totalCnt)
			}
		} else {
			exerciseAllData = "-"
		}

		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, exerciseAllData)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【获取观看互动题数据】", "CommonLU: inclass_right_cnt, inclass_participate_cnt, playback_right_cnt, playback_participate_cnt, inclass_question_cnt")
	return
}

// GetLbpInteractExamColumn 获取LBP互动题数据
// 对应PHP中的lbpInteractExam字段，格式化为"X/Y/Z"（正确数/回答数/总数）
func (s *Format) GetLbpInteractExamColumn(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceLu, []string{}) {
		return
	}

	// 获取学生课程数据 - LBP互动题相关字段
	queryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLuData", []interface{}{
		[]int64{s.param.CourseID},
		[]int64{s.param.StudentUid},
		[]string{"mix_live_interaction_right_num", "mix_live_interaction_submit_num"},
	})
	if err != nil {
		return
	}

	// 获取公共LU数据 - 互动题总数（支持灰度逻辑）
	commonLuQueryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLuData", []interface{}{
		s.param.CourseID,
		[]int64{s.param.StudentUid},
		[]string{"inclass_question_cnt"},
	})
	if err != nil {
		return
	}

	studentLessonData := queryData.(map[string]map[string]interface{})
	commonLuData := commonLuQueryData.(map[string]map[string]interface{})

	for _, lessonID := range s.param.LessonIDs {
		lbpInteractExamData := ""
		key := fmt.Sprintf("%d_%d_%d", s.param.CourseID, lessonID, s.param.StudentUid)

		rightNum := int64(0)
		submitNum := int64(0)
		totalNum := int64(0)

		// 获取LBP互动题正确数和提交数
		if lessonData, ok := studentLessonData[key]; ok {
			if rightNumVal, exists := lessonData["mix_live_interaction_right_num"]; exists {
				if val, ok := rightNumVal.(int64); ok {
					rightNum = val
				}
			}

			if submitNumVal, exists := lessonData["mix_live_interaction_submit_num"]; exists {
				if val, ok := submitNumVal.(int64); ok {
					submitNum = val
				}
			}
		}

		// 获取互动题总数 - 支持灰度逻辑
		if lessonLuData, ok := commonLuData[key]; ok {
			if questionCntVal, exists := lessonLuData["inclass_question_cnt"]; exists {
				if val, ok := questionCntVal.(int64); ok {
					totalNum = val
				}
			}
		}

		// 如果总数为0，则显示"-"
		if totalNum == 0 {
			lbpInteractExamData = "-"
		} else {
			// 格式化为 "X/Y/Z" 格式
			lbpInteractExamData = fmt.Sprintf("%d/%d/%d", rightNum, submitNum, totalNum)
		}

		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, lbpInteractExamData)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【获取LBP互动题数据】", "ES: mix_live_interaction_right_num, mix_live_interaction_submit_num + CommonLU: inclass_question_cnt")
	return
}

// formatPlaybackTime 格式化回放时长为"XminYs"格式
func (s *Format) formatPlaybackTime(totalSeconds int64) string {
	if totalSeconds <= 0 {
		return "-"
	}

	minutes := totalSeconds / 60
	seconds := totalSeconds % 60

	if seconds > 0 {
		return fmt.Sprintf("%dmin%ds", minutes, seconds)
	}
	return fmt.Sprintf("%dmin", minutes)
}

// formatDurationTime 格式化时长（通用方法）
func (s *Format) formatDurationTime(totalSeconds int64) string {
	if totalSeconds <= 0 {
		return "-"
	}

	hours := totalSeconds / 3600
	minutes := (totalSeconds % 3600) / 60
	seconds := totalSeconds % 60

	if hours > 0 {
		return fmt.Sprintf("%d:%02d:%02d", hours, minutes, seconds)
	} else if minutes > 0 {
		return fmt.Sprintf("%d:%02d", minutes, seconds)
	}
	return fmt.Sprintf("0:%02d", seconds)
}
