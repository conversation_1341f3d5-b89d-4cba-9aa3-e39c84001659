package dataQuery

import (
	"deskcrm/models"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

// GetLessonStudentData 获取学生章节请假信息
func (s *Singleton) GetLessonStudentData(ctx *gin.Context, courseId int64, lessonIds []int64, studentUids []int64) (map[string]*models.LessonStudent, error) {
	if len(lessonIds) == 0 || len(studentUids) == 0 || courseId == 0 {
		return make(map[string]*models.LessonStudent), nil
	}

	data, err := models.LessonStudentDao.GetListByLessonIds(ctx, courseId, lessonIds, studentUids)
	if err != nil {
		zlog.Warnf(ctx, "GetLessonStudentData failed: %v", err)
		return nil, err
	}

	return data, nil
}
