package dataQuery

import (
	"deskcrm/api/assistantdeskgo"
	"deskcrm/api/dataproxy"
	"fmt"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

// GetLuData 获取课程学生的LU数据
func (s *Singleton) GetLuData(ctx *gin.Context, courseIds []int64, studentUids []int64, fields []string) (map[int64]*dataproxy.GetListByCourseIdsStudentUidsResp, error) {
	if len(courseIds) == 0 || len(studentUids) == 0 || len(fields) == 0 {
		return make(map[int64]*dataproxy.GetListByCourseIdsStudentUidsResp), nil
	}

	client := dataproxy.NewClient()
	params := dataproxy.GetListByCourseIdsStudentUidsParam{
		CourseIds:   courseIds,
		StudentUids: studentUids,
		Fields:      fields,
	}

	data, err := client.GetListByCourseIdsStudentUids(ctx, params)
	if err != nil {
		zlog.Warnf(ctx, "GetCommonLuDataByLessonStudents failed: %v", err)
		return nil, err
	}

	result := make(map[int64]*dataproxy.GetListByCourseIdsStudentUidsResp)
	for _, item := range data {
		result[item.LessonId] = item
	}

	return result, nil
}

// GetLuData 获取课程学生的公共LU数据
func (s *Singleton) GetCommonLuData(ctx *gin.Context, lessonIds []int64, studentUid int64, fields []string) (map[int64]*dataproxy.GetLuComonListByStudentLessonsResp, error) {
	if len(lessonIds) == 0 || studentUid == 0 || len(fields) == 0 {
		return make(map[int64]*dataproxy.GetLuComonListByStudentLessonsResp), nil
	}

	client := dataproxy.NewClient()
	params := dataproxy.GetLuComonListByStudentLessonsParam{
		LessonIds:  lessonIds,
		StudentUid: studentUid,
		Fields:     fields,
	}

	data, err := client.GetLuComonListByStudentLessons(ctx, params)
	if err != nil {
		zlog.Warnf(ctx, "GetCommonLuDataByLessonStudents failed: %v", err)
		return nil, err
	}

	result := make(map[int64]*dataproxy.GetLuComonListByStudentLessonsResp)
	for _, item := range data {
		result[item.LessonId] = item
	}

	return result, nil
}

// GetExamRelationData 获取试卷绑定数据
func (s *Singleton) GetExamRelationData(ctx *gin.Context, bindIds []int64, bindType int, relationTypes []int, fields []string) (map[string]interface{}, error) {
	if len(bindIds) == 0 || bindType == 0 || len(fields) == 0 {
		return make(map[string]interface{}), nil
	}

	client := dataproxy.NewClient()

	params := dataproxy.GetListByBindIdsBindTypeRelationTypesCommonParam{
		BindIds:       bindIds,
		BindType:      int64(bindType),
		RelationTypes: relationTypes,
		Fields:        fields,
	}

	data, err := client.GetListByBindIdsBindTypeRelationTypes(ctx, params)
	if err != nil {
		zlog.Warnf(ctx, "GetExamRelationData failed: %v", err)
		return nil, err
	}

	result := make(map[string]interface{})
	for _, item := range data {
		result[fmt.Sprintf("%d", item.BindId)] = item
	}

	return result, nil
}

func (s *Singleton) GetLearnReportByClueGradeIds(ctx *gin.Context, clueIdGradeIdMap map[string]int64) (getLessonListResp map[string]assistantdeskgo.LearnReportInfo, err error) {
	leadsInfoMap, err := assistantdeskgo.NewClient().GetLearnReportByClueGradeIds(ctx, clueIdGradeIdMap)
	if err != nil {
		zlog.Warnf(ctx, "GetPrivateLeadsData failed: %s", err.Error())
		return leadsInfoMap, nil
	}
	return leadsInfoMap, nil
}
