package models

import (
	"deskcrm/helpers"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// 请假状态常量
const (
	PreAttendOk               = 0 // 未填
	PreAttendLeave            = 1 // 请假
	PreAttendNoContact        = 2 // 失联
	PreAttendPlayback         = 3 // 回放
	PreAttendEquipmentProblem = 4 // 设备问题
	PreAttendNormal           = 5 // 能到
	PreAttendAccompany        = 6 // 伴学
	PreAttendUndetermined     = 7 // 未确定
)

// 请假状态描述
var PreAttendTextMap = map[int]string{
	PreAttendOk:               "未填",
	PreAttendLeave:            "请假",
	PreAttendNoContact:        "失联",
	PreAttendPlayback:         "回放",
	PreAttendEquipmentProblem: "设备问题",
	PreAttendNormal:           "能到",
	PreAttendAccompany:        "伴学",
	PreAttendUndetermined:     "未确定",
}

// LessonStudentExtData 请假扩展数据
type LessonStudentExtData struct {
	LeaveSeason      string `json:"leaveSeason"`      // 请假理由
	FirstLeaveReason string `json:"firstLeaveReason"` // 一级请假理由
	ContentTime      string `json:"contentTime"`      // 观看回放时间
	IsSyncRemind     int    `json:"isSyncRemind"`     // 是否同步到待办
	RemindTime       string `json:"remindTime"`       // 待办提醒时间
	RemindId         string `json:"remindId"`         // 待办ID
	PreAttendTime    int64  `json:"preAttendTime"`    // 预到课标记时间
}

// LessonStudent 学生章节信息
type LessonStudent struct {
	ID           int64                `gorm:"primary_key;column:id" json:"id"`
	StudentUid   int64                `gorm:"column:student_uid" json:"studentUid"`
	CourseId     int64                `gorm:"column:course_id" json:"courseId"`
	LessonId     int64                `gorm:"column:lesson_id" json:"lessonId"`
	AssistantUid int64                `gorm:"column:assistant_uid" json:"assistantUid"`
	ClassId      int64                `gorm:"column:class_id" json:"classId"`
	PreAttend    int                  `gorm:"column:pre_attend" json:"preAttend"` // 预到课状态
	Status       int                  `gorm:"column:status" json:"status"`        // 状态
	CreateTime   int64                `gorm:"column:create_time" json:"createTime"`
	UpdateTime   int64                `gorm:"column:update_time" json:"updateTime"`
	ExtData      LessonStudentExtData `gorm:"column:ext_data;type:json" json:"extData"` // 扩展数据
	Focus        int                  `gorm:"column:focus" json:"focus"`                // 关注
}

var LessonStudentDao lessonStudentDao

type lessonStudentDao struct {
}

// TableName 指定表名
func (LessonStudent) TableName() string {
	return TblLessonStudent
}

// GetByLessonStudent 根据课程ID、章节ID、学生ID获取记录
func (d *lessonStudentDao) GetByLessonStudent(ctx *gin.Context, courseId, lessonId, studentUid int64) (*LessonStudent, error) {
	var result LessonStudent
	err := helpers.MysqlClient.Where("course_id = ? AND lesson_id = ? AND student_uid = ?", courseId, lessonId, studentUid).First(&result).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &result, nil
}

// GetListByLessonIds 根据章节ID列表获取学生数据
func (d *lessonStudentDao) GetListByLessonIds(ctx *gin.Context, courseId int64, lessonIds []int64, studentUids []int64) (map[string]*LessonStudent, error) {
	if len(lessonIds) == 0 || len(studentUids) == 0 {
		return make(map[string]*LessonStudent), nil
	}

	var results []LessonStudent
	err := helpers.MysqlClient.Where("course_id = ? AND lesson_id IN ? AND student_uid IN ?", courseId, lessonIds, studentUids).Find(&results).Error
	if err != nil {
		return nil, err
	}

	// 构建映射表 key: "lessonId_studentUid"
	resultMap := make(map[string]*LessonStudent)
	for i := range results {
		key := GetLessonStudentKey(results[i].LessonId, results[i].StudentUid)
		resultMap[key] = &results[i]
	}

	return resultMap, nil
}

// GetLessonStudentKey 生成学生章节数据的映射键
func GetLessonStudentKey(lessonId, studentUid int64) string {
	return helpers.DataDigest(map[string]interface{}{
		"lessonId":   lessonId,
		"studentUid": studentUid,
	}, "lesson_student")
}
